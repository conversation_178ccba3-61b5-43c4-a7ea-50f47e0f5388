<template>
  <div class="export-modal" v-if="isVisible" @click="handleBackdropClick">
    <div class="export-modal__content" @click.stop>
      <div class="export-modal__header">
        <h2>
          <Download :size="24" />
          Экспорт дизайна
        </h2>
        <button class="export-modal__close" @click="$emit('close')">
          <X :size="20" />
        </button>
      </div>

      <div class="export-modal__body">
        <!-- Format Selection -->
        <div class="export-section">
          <h3>
            <FileImage :size="18" />
            Формат файла
          </h3>
          <div class="format-grid">
            <button
              v-for="format in formats"
              :key="format.id"
              class="format-card"
              :class="{ active: selectedFormat === format.id }"
              @click="selectedFormat = format.id"
            >
              <div class="format-card__icon">
                <component :is="format.icon" :size="24" />
              </div>
              <div class="format-card__info">
                <h4>{{ format.name }}</h4>
                <p>{{ format.description }}</p>
                <span class="format-card__size">{{ format.maxSize }}</span>
              </div>
            </button>
          </div>
        </div>

        <!-- Quality Settings -->
        <div class="export-section" v-if="selectedFormat !== 'svg'">
          <h3>
            <Settings :size="18" />
            Качество
          </h3>
          <div class="quality-control">
            <div class="quality-slider">
              <label>Разрешение: {{ selectedQuality }}x</label>
              <input
                type="range"
                v-model="selectedQuality"
                min="1"
                max="4"
                step="0.5"
                class="slider"
              />
              <div class="quality-labels">
                <span>1x</span>
                <span>2x</span>
                <span>3x</span>
                <span>4x</span>
              </div>
            </div>
            <div class="quality-info">
              <p>Размер: {{ calculatedWidth }} × {{ calculatedHeight }} px</p>
              <p class="file-size">Примерный размер: {{ estimatedFileSize }}</p>
            </div>
          </div>
        </div>

        <!-- Background Options -->
        <div class="export-section">
          <h3>
            <Palette :size="18" />
            Фон
          </h3>
          <div class="background-options">
            <label class="checkbox-option">
              <input type="checkbox" v-model="includeBackground" />
              <span class="checkmark"></span>
              Включить фон
            </label>
            <label class="checkbox-option" v-if="selectedFormat === 'png'">
              <input
                type="checkbox"
                v-model="transparentBackground"
                :disabled="includeBackground"
              />
              <span class="checkmark"></span>
              Прозрачный фон
            </label>
          </div>
        </div>

        <!-- Telegram Integration -->
        <div class="export-section" v-if="isTelegramWebApp">
          <h3>
            <Send :size="18" />
            Отправка в Telegram
          </h3>
          <div class="telegram-options">
            <label class="checkbox-option">
              <input type="checkbox" v-model="sendToTelegram" />
              <span class="checkmark"></span>
              Отправить в чат
            </label>
            <div v-if="sendToTelegram" class="telegram-caption">
              <label>Подпись к изображению:</label>
              <textarea
                v-model="telegramCaption"
                placeholder="Добавьте подпись к вашему дизайну..."
                rows="3"
                maxlength="1024"
              ></textarea>
              <span class="char-count">{{ telegramCaption.length }}/1024</span>
            </div>
          </div>
        </div>

        <!-- Preview -->
        <div class="export-section">
          <h3>
            <Eye :size="18" />
            Предварительный просмотр
          </h3>
          <div class="preview-container">
            <canvas
              ref="previewCanvas"
              class="preview-canvas"
              :style="{ aspectRatio: `${canvasWidth} / ${canvasHeight}` }"
            ></canvas>
          </div>
        </div>
      </div>

      <div class="export-modal__footer">
        <button class="btn btn--secondary" @click="$emit('close')">Отмена</button>
        <button class="btn btn--primary" @click="handleExport" :disabled="isExporting">
          <component
            :is="isExporting ? 'Loader2' : 'Download'"
            :size="18"
            :class="{ spinning: isExporting }"
          />
          {{ isExporting ? 'Экспорт...' : 'Экспортировать' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import {
  Download,
  X,
  FileImage,
  Settings,
  Palette,
  Send,
  Eye,
  Loader2,
  Image,
  FileText,
  Layers,
} from 'lucide-vue-next'

export default {
  name: 'ExportModal',
  components: {
    Download,
    X,
    FileImage,
    Settings,
    Palette,
    Send,
    Eye,
    Loader2,
    Image,
    FileText,
    Layers,
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['close', 'export'],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { webApp, hapticFeedback } = useTelegram()

    const previewCanvas = ref(null)
    const isExporting = ref(false)

    // Export settings
    const selectedFormat = ref('png')
    const selectedQuality = ref(2)
    const includeBackground = ref(true)
    const transparentBackground = ref(false)
    const sendToTelegram = ref(false)
    const telegramCaption = ref('')

    // Available formats
    const formats = [
      {
        id: 'png',
        name: 'PNG',
        description: 'Высокое качество, поддержка прозрачности',
        icon: 'Image',
        maxSize: 'до 10MB',
      },
      {
        id: 'jpg',
        name: 'JPEG',
        description: 'Меньший размер файла, без прозрачности',
        icon: 'Image',
        maxSize: 'до 5MB',
      },
      {
        id: 'svg',
        name: 'SVG',
        description: 'Векторный формат, масштабируемый',
        icon: 'Layers',
        maxSize: 'до 1MB',
      },
    ]

    // Computed properties
    const canvasWidth = computed(() => canvasStore.width)
    const canvasHeight = computed(() => canvasStore.height)
    const isTelegramWebApp = computed(() => !!webApp.value)

    const calculatedWidth = computed(() => Math.round(canvasWidth.value * selectedQuality.value))
    const calculatedHeight = computed(() => Math.round(canvasHeight.value * selectedQuality.value))

    const estimatedFileSize = computed(() => {
      const pixels = calculatedWidth.value * calculatedHeight.value
      let bytesPerPixel = 4 // RGBA

      if (selectedFormat.value === 'jpg') {
        bytesPerPixel = 0.5 // JPEG compression
      } else if (selectedFormat.value === 'svg') {
        return '< 100KB'
      }

      const bytes = pixels * bytesPerPixel
      if (bytes < 1024 * 1024) {
        return Math.round(bytes / 1024) + 'KB'
      } else {
        return (bytes / (1024 * 1024)).toFixed(1) + 'MB'
      }
    })

    // Watch for transparency changes
    watch(transparentBackground, (value) => {
      if (value) {
        includeBackground.value = false
      }
    })

    watch(includeBackground, (value) => {
      if (value) {
        transparentBackground.value = false
      }
    })

    // Generate preview
    const generatePreview = async () => {
      if (!previewCanvas.value) return

      const canvas = previewCanvas.value
      const ctx = canvas.getContext('2d')

      // Set canvas size
      const maxSize = 300
      const aspectRatio = canvasWidth.value / canvasHeight.value
      let previewWidth = maxSize
      let previewHeight = maxSize / aspectRatio

      if (previewHeight > maxSize) {
        previewHeight = maxSize
        previewWidth = maxSize * aspectRatio
      }

      canvas.width = previewWidth
      canvas.height = previewHeight

      // Scale factor
      const scale = previewWidth / canvasWidth.value

      ctx.scale(scale, scale)

      // Render background
      if (includeBackground.value && !transparentBackground.value) {
        ctx.fillStyle = canvasStore.background.value || '#ffffff'
        ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)
      } else {
        ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
      }

      // Render elements (simplified)
      canvasStore.elements.forEach((element) => {
        ctx.save()

        if (element.type === 'text') {
          ctx.font = `${element.fontWeight || 'normal'} ${element.fontStyle || 'normal'} ${element.fontSize}px ${element.fontFamily}`
          ctx.fillStyle = element.color
          ctx.textAlign = element.textAlign || 'left'
          ctx.textBaseline = 'top'

          let textX = element.x
          if (element.textAlign === 'center') {
            textX += element.width / 2
          } else if (element.textAlign === 'right') {
            textX += element.width
          }

          ctx.fillText(element.text, textX, element.y)
        }

        ctx.restore()
      })
    }

    // Handle export
    const handleExport = async () => {
      isExporting.value = true
      hapticFeedback.impactOccurred('medium')

      try {
        const exportData = {
          format: selectedFormat.value,
          quality: selectedQuality.value,
          includeBackground: includeBackground.value,
          transparentBackground: transparentBackground.value,
          width: calculatedWidth.value,
          height: calculatedHeight.value,
          sendToTelegram: sendToTelegram.value,
          telegramCaption: telegramCaption.value,
        }

        emit('export', exportData)

        // Close modal after successful export
        setTimeout(() => {
          emit('close')
        }, 1000)
      } catch (error) {
        console.error('Export failed:', error)
        hapticFeedback.notificationOccurred('error')
      } finally {
        isExporting.value = false
      }
    }

    const handleBackdropClick = () => {
      emit('close')
    }

    // Generate preview when modal opens
    watch(
      () => props.isVisible,
      (visible) => {
        if (visible) {
          setTimeout(generatePreview, 100)
        }
      },
    )

    // Update preview when settings change
    watch([includeBackground, transparentBackground], generatePreview)

    onMounted(() => {
      if (props.isVisible) {
        generatePreview()
      }
    })

    return {
      previewCanvas,
      isExporting,
      selectedFormat,
      selectedQuality,
      includeBackground,
      transparentBackground,
      sendToTelegram,
      telegramCaption,
      formats,
      canvasWidth,
      canvasHeight,
      isTelegramWebApp,
      calculatedWidth,
      calculatedHeight,
      estimatedFileSize,
      handleExport,
      handleBackdropClick,
    }
  },
}
</script>

<style lang="scss" scoped>
.export-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);

  &__content {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 24px;

    h2 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      font-size: 24px;
      font-weight: 700;
      color: #1a1a1a;
    }
  }

  &__close {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 12px;
    background: transparent;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }
  }

  &__body {
    padding: 0 24px;
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-top: 1px solid #f0f0f0;
    margin-top: 24px;
    gap: 12px;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.export-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 16px;
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
  }
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

.format-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;

  &:hover {
    border-color: #007aff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
  }

  &.active {
    border-color: #007aff;
    background: rgba(0, 122, 255, 0.05);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
  }

  &__icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    color: #007aff;
  }

  &__info {
    flex: 1;
    min-width: 0;

    h4 {
      margin: 0 0 4px;
      font-size: 14px;
      font-weight: 600;
      color: #1a1a1a;
    }

    p {
      margin: 0 0 4px;
      font-size: 12px;
      color: #666;
      line-height: 1.3;
    }
  }

  &__size {
    font-size: 11px;
    color: #999;
    font-weight: 500;
  }
}

.quality-control {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.quality-slider {
  flex: 1;

  label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: #1a1a1a;
  }

  .slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e5e5e5;
    outline: none;
    margin-bottom: 8px;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #007aff;
      cursor: pointer;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }
  }

  .quality-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
  }
}

.quality-info {
  flex-shrink: 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  min-width: 160px;

  p {
    margin: 0 0 8px;
    font-size: 14px;
    color: #333;

    &:last-child {
      margin-bottom: 0;
    }

    &.file-size {
      font-weight: 600;
      color: #007aff;
    }
  }
}

.background-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;

  input[type='checkbox'] {
    display: none;
  }

  .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e5e5;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;

    &::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 2px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }

  input[type='checkbox']:checked + .checkmark {
    background: #007aff;
    border-color: #007aff;

    &::after {
      opacity: 1;
    }
  }

  input[type='checkbox']:disabled + .checkmark {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.telegram-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.telegram-caption {
  margin-left: 32px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #1a1a1a;
    font-size: 14px;
  }

  textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;

    &:focus {
      outline: none;
      border-color: #007aff;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }
  }

  .char-count {
    display: block;
    text-align: right;
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }
}

.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #e5e5e5;
}

.preview-canvas {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: white;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }

  &--primary {
    background: #007aff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056cc;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
    }
  }

  &--secondary {
    background: #f8f9fa;
    color: #666;

    &:hover:not(:disabled) {
      background: #e9ecef;
      transform: translateY(-2px);
    }
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .export-modal {
    padding: 12px;

    &__content {
      border-radius: 16px;
      max-height: 95vh;
    }

    &__header {
      padding: 20px 20px 0;

      h2 {
        font-size: 20px;
      }
    }

    &__body {
      padding: 0 20px;
    }

    &__footer {
      padding: 20px;
      flex-direction: column;
      gap: 12px;

      .btn {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .format-grid {
    grid-template-columns: 1fr;
  }

  .quality-control {
    flex-direction: column;
    gap: 16px;
  }

  .quality-info {
    min-width: auto;
  }
}
</style>

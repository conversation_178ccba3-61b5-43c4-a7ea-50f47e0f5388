// Color palette
$primary-color: #007aff;
$secondary-color: #5856d6;
$success-color: #34c759;
$warning-color: #ff9500;
$error-color: #ff3b30;
$background-color: #f2f2f7;
$surface-color: #ffffff;
$text-primary: #000000;
$text-secondary: #8e8e93;
$border-color: #c6c6c8;

// Telegram theme colors (will be overridden by Telegram WebApp)
$tg-bg-color: var(--tg-theme-bg-color, #{$background-color});
$tg-text-color: var(--tg-theme-text-color, #{$text-primary});
$tg-hint-color: var(--tg-theme-hint-color, #{$text-secondary});
$tg-link-color: var(--tg-theme-link-color, #{$primary-color});
$tg-button-color: var(--tg-theme-button-color, #{$primary-color});
$tg-button-text-color: var(--tg-theme-button-text-color, #ffffff);

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

// Typography
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-xxxl: 32px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Z-index
$z-index-dropdown: 1000;
$z-index-modal: 1050;
$z-index-tooltip: 1070;

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

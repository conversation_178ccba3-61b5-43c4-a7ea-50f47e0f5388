<template>
  <div class="shapes-panel">
    <div class="shapes-grid">
      <button
        v-for="shape in shapes"
        :key="shape.id"
        class="shape-btn"
        @click="addShape(shape)"
      >
        <component :is="shape.icon" :size="32" />
        <span>{{ shape.name }}</span>
      </button>
    </div>
  </div>
</template>

<script>
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import {
  Square,
  Circle,
  Triangle,
  Star,
  Heart,
  Hexagon
} from 'lucide-vue-next'

export default {
  name: 'ShapesPanel',
  components: {
    Square,
    Circle,
    Triangle,
    Star,
    Heart,
    Hexagon
  },
  emits: ['close'],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()
    
    const shapes = [
      { id: 'rectangle', name: 'Прямоугольник', icon: 'Square', type: 'rectangle' },
      { id: 'circle', name: 'Круг', icon: 'Circle', type: 'circle' },
      { id: 'triangle', name: 'Треугольник', icon: 'Triangle', type: 'triangle' },
      { id: 'star', name: 'Звезда', icon: 'Star', type: 'star' },
      { id: 'heart', name: 'Сердце', icon: 'Heart', type: 'heart' },
      { id: 'hexagon', name: 'Шестиугольник', icon: 'Hexagon', type: 'hexagon' }
    ]
    
    const addShape = (shape) => {
      const element = {
        type: 'shape',
        shapeType: shape.type,
        x: 100,
        y: 100,
        width: 100,
        height: 100,
        fill: '#667eea',
        stroke: '#374151',
        strokeWidth: 2,
        opacity: 1,
        rotation: 0
      }
      
      canvasStore.addElement(element)
      hapticFeedback.impactOccurred('medium')
      emit('close')
    }
    
    return {
      shapes,
      addShape
    }
  }
}
</script>

<style lang="scss" scoped>
.shapes-panel {
  padding: 20px;
}

.shapes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 16px;
}

.shape-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  }

  span {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    text-align: center;
  }
}
</style>

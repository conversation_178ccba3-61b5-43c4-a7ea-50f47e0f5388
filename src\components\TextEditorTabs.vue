<template>
  <div class="text-editor-tabs" v-if="selectedElement && selectedElement.type === 'text'">
    <div class="text-editor-tabs__header" @click="toggleCollapse">
      <div class="text-editor-tabs__header-content">
        <Type class="text-editor-tabs__icon" :size="20" />
        <h3>Редактирование текста</h3>
      </div>
      <div class="text-editor-tabs__header-actions">
        <button
          class="text-editor-tabs__toggle"
          @click.stop="toggleCollapse"
          :class="{ collapsed: isCollapsed }"
        >
          <ChevronDown :size="20" />
        </button>
        <button class="text-editor-tabs__close" @click="$emit('close')">
          <X :size="18" />
        </button>
      </div>
    </div>

    <Transition name="collapse">
      <div class="text-editor-tabs__content" v-show="!isCollapsed">
        <!-- Tab Navigation -->
        <div class="text-editor-tabs__nav">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-button"
            :class="{ active: activeTab === tab.id }"
            @click="activeTab = tab.id"
          >
            <component :is="tab.icon" :size="16" />
            {{ tab.name }}
          </button>
        </div>

        <!-- Tab Content -->
        <div class="text-editor-tabs__body">
          <!-- Font Tab -->
          <div v-if="activeTab === 'font'" class="tab-content">
            <div class="field-group">
              <label>Текст</label>
              <textarea
                v-model="localText"
                @input="updateText"
                placeholder="Введите текст..."
                rows="3"
                class="text-input"
              />
            </div>

            <div class="field-group">
              <label>Шрифт</label>
              <select v-model="localFontFamily" @change="updateFontFamily" class="font-select">
                <option value="Arial, sans-serif">Arial</option>
                <option value="Helvetica, sans-serif">Helvetica</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="Times New Roman, serif">Times New Roman</option>
                <option value="Courier New, monospace">Courier New</option>
                <option value="Impact, sans-serif">Impact</option>
                <option value="Comic Sans MS, cursive">Comic Sans MS</option>
              </select>
            </div>

            <div class="field-group">
              <label>Размер шрифта</label>
              <div class="size-control">
                <input
                  type="range"
                  v-model="localFontSize"
                  @input="updateFontSize"
                  min="12"
                  max="120"
                  step="2"
                  class="size-slider"
                />
                <input
                  type="number"
                  v-model="localFontSize"
                  @input="updateFontSize"
                  min="12"
                  max="120"
                  class="size-input"
                />
              </div>
            </div>

            <!-- Font Preview -->
            <div class="font-preview">
              <div
                class="preview-text"
                :style="{
                  fontFamily: localFontFamily,
                  fontSize: localFontSize + 'px',
                  fontWeight: localFontWeight,
                  fontStyle: localFontStyle,
                }"
              >
                {{ localText || 'Пример текста' }}
              </div>
            </div>
          </div>

          <!-- Style Tab -->
          <div v-if="activeTab === 'style'" class="tab-content">
            <div class="style-controls">
              <div class="field-group">
                <label>Начертание</label>
                <div class="style-buttons">
                  <button
                    class="style-btn"
                    :class="{ active: localFontWeight === 'bold' }"
                    @click="toggleBold"
                  >
                    <Bold :size="16" />
                  </button>
                  <button
                    class="style-btn"
                    :class="{ active: localFontStyle === 'italic' }"
                    @click="toggleItalic"
                  >
                    <Italic :size="16" />
                  </button>
                </div>
              </div>

              <div class="field-group">
                <label>Выравнивание</label>
                <div class="align-buttons">
                  <button
                    class="align-btn"
                    :class="{ active: localTextAlign === 'left' }"
                    @click="updateTextAlign('left')"
                  >
                    <AlignLeft :size="16" />
                  </button>
                  <button
                    class="align-btn"
                    :class="{ active: localTextAlign === 'center' }"
                    @click="updateTextAlign('center')"
                  >
                    <AlignCenter :size="16" />
                  </button>
                  <button
                    class="align-btn"
                    :class="{ active: localTextAlign === 'right' }"
                    @click="updateTextAlign('right')"
                  >
                    <AlignRight :size="16" />
                  </button>
                </div>
              </div>

              <div class="field-group">
                <label>Цвет текста</label>
                <div class="color-control">
                  <input
                    type="color"
                    v-model="localColor"
                    @input="updateColor"
                    class="color-picker"
                  />
                  <input
                    type="text"
                    v-model="localColor"
                    @input="updateColor"
                    placeholder="#000000"
                    class="color-input"
                  />
                </div>
              </div>

              <div class="field-group">
                <label>Обводка</label>
                <div class="stroke-controls">
                  <div class="color-control">
                    <input
                      type="color"
                      v-model="localStrokeColor"
                      @input="updateStrokeColor"
                      class="color-picker"
                    />
                    <input
                      type="text"
                      v-model="localStrokeColor"
                      @input="updateStrokeColor"
                      placeholder="#000000"
                      class="color-input"
                    />
                  </div>
                  <div class="stroke-width">
                    <label>Толщина</label>
                    <input
                      type="range"
                      v-model="localStrokeWidth"
                      @input="updateStrokeWidth"
                      min="0"
                      max="10"
                      step="1"
                      class="stroke-slider"
                    />
                    <span class="value-display">{{ localStrokeWidth }}px</span>
                  </div>
                </div>
              </div>

              <div class="field-group">
                <label>Тень</label>
                <div class="shadow-controls">
                  <div class="color-control">
                    <input
                      type="color"
                      v-model="localShadowColor"
                      @input="updateShadowColor"
                      class="color-picker"
                    />
                    <input
                      type="text"
                      v-model="localShadowColor"
                      @input="updateShadowColor"
                      placeholder="#000000"
                      class="color-input"
                    />
                  </div>
                  <div class="shadow-params">
                    <div class="param-control">
                      <label>Размытие</label>
                      <input
                        type="range"
                        v-model="localShadowBlur"
                        @input="updateShadowBlur"
                        min="0"
                        max="20"
                        step="1"
                      />
                      <span class="value-display">{{ localShadowBlur }}px</span>
                    </div>
                    <div class="param-control">
                      <label>Смещение X</label>
                      <input
                        type="range"
                        v-model="localShadowOffsetX"
                        @input="updateShadowOffsetX"
                        min="-20"
                        max="20"
                        step="1"
                      />
                      <span class="value-display">{{ localShadowOffsetX }}px</span>
                    </div>
                    <div class="param-control">
                      <label>Смещение Y</label>
                      <input
                        type="range"
                        v-model="localShadowOffsetY"
                        @input="updateShadowOffsetY"
                        min="-20"
                        max="20"
                        step="1"
                      />
                      <span class="value-display">{{ localShadowOffsetY }}px</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Position Tab -->
          <div v-if="activeTab === 'position'" class="tab-content">
            <div class="position-controls">
              <div class="field-group">
                <label>Быстрое позиционирование</label>
                <div class="position-grid">
                  <button
                    v-for="pos in quickPositions"
                    :key="pos.id"
                    class="position-btn"
                    @click="applyQuickPosition(pos)"
                    :title="pos.name"
                  >
                    <component :is="pos.icon" :size="16" />
                    <span>{{ pos.name }}</span>
                  </button>
                </div>
              </div>

              <div class="field-group">
                <label>Точное позиционирование</label>
                <div class="precise-controls">
                  <div class="coord-control">
                    <label>X</label>
                    <input
                      type="number"
                      :value="selectedElement?.x || 0"
                      @input="updatePosition('x', $event.target.value)"
                      class="coord-input"
                    />
                  </div>
                  <div class="coord-control">
                    <label>Y</label>
                    <input
                      type="number"
                      :value="selectedElement?.y || 0"
                      @input="updatePosition('y', $event.target.value)"
                      class="coord-input"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script>
import { ref, watch, computed } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import {
  Type,
  X,
  ChevronDown,
  Palette,
  Square,
  Layers,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Move,
  AlignJustify,
  CornerUpLeft,
  CornerUpRight,
  CornerDownLeft,
  CornerDownRight,
} from 'lucide-vue-next'

export default {
  name: 'TextEditorTabs',
  components: {
    Type,
    X,
    ChevronDown,
    Palette,
    Square,
    Layers,
    AlignLeft,
    AlignCenter,
    AlignRight,
    Bold,
    Italic,

    Move,
    AlignJustify,
    CornerUpLeft,
    CornerUpRight,
    CornerDownLeft,
    CornerDownRight,
  },
  emits: ['close'],
  setup() {
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()

    const selectedElement = computed(() => canvasStore.selectedElement)

    // Collapse state
    const isCollapsed = ref(false)
    const activeTab = ref('font')

    // Tabs configuration
    const tabs = [
      { id: 'font', name: 'Шрифт', icon: 'Type' },
      { id: 'style', name: 'Стиль', icon: 'Palette' },
      { id: 'position', name: 'Позиция', icon: 'Move' },
    ]

    // Quick position options
    const quickPositions = [
      { id: 'top-left', name: 'Верх слева', icon: 'CornerUpLeft', x: 50, y: 50 },
      { id: 'top-center', name: 'Верх центр', icon: 'AlignJustify', x: 'center', y: 50 },
      { id: 'top-right', name: 'Верх справа', icon: 'CornerUpRight', x: 'right', y: 50 },
      { id: 'center-left', name: 'Центр слева', icon: 'AlignLeft', x: 50, y: 'center' },
      { id: 'center', name: 'По центру', icon: 'Center', x: 'center', y: 'center' },
      { id: 'center-right', name: 'Центр справа', icon: 'AlignRight', x: 'right', y: 'center' },
      { id: 'bottom-left', name: 'Низ слева', icon: 'CornerDownLeft', x: 50, y: 'bottom' },
      { id: 'bottom-center', name: 'Низ центр', icon: 'AlignJustify', x: 'center', y: 'bottom' },
      { id: 'bottom-right', name: 'Низ справа', icon: 'CornerDownRight', x: 'right', y: 'bottom' },
    ]

    // Local reactive properties
    const localText = ref('')
    const localFontFamily = ref('Arial, sans-serif')
    const localFontSize = ref(24)
    const localTextAlign = ref('left')
    const localFontWeight = ref('normal')
    const localFontStyle = ref('normal')
    const localColor = ref('#000000')
    const localStrokeColor = ref('#000000')
    const localStrokeWidth = ref(0)
    const localShadowColor = ref('#000000')
    const localShadowBlur = ref(0)
    const localShadowOffsetX = ref(0)
    const localShadowOffsetY = ref(0)

    // Watch for element changes
    watch(
      selectedElement,
      (element) => {
        if (element && element.type === 'text') {
          localText.value = element.text || ''
          localFontFamily.value = element.fontFamily || 'Arial, sans-serif'
          localFontSize.value = element.fontSize || 24
          localTextAlign.value = element.textAlign || 'left'
          localFontWeight.value = element.fontWeight || 'normal'
          localFontStyle.value = element.fontStyle || 'normal'
          localColor.value = element.color || '#000000'
          localStrokeColor.value = element.strokeColor || '#000000'
          localStrokeWidth.value = element.strokeWidth || 0
          localShadowColor.value = element.shadowColor || '#000000'
          localShadowBlur.value = element.shadowBlur || 0
          localShadowOffsetX.value = element.shadowOffsetX || 0
          localShadowOffsetY.value = element.shadowOffsetY || 0
        }
      },
      { immediate: true },
    )

    // Update methods
    const updateElement = (updates) => {
      if (selectedElement.value) {
        canvasStore.updateElement(selectedElement.value.id, updates)
      }
    }

    const updateText = () => {
      updateElement({ text: localText.value })
      hapticFeedback.selectionChanged()
    }

    const updateFontFamily = () => {
      updateElement({ fontFamily: localFontFamily.value })
      hapticFeedback.selectionChanged()
    }

    const updateFontSize = () => {
      updateElement({ fontSize: parseInt(localFontSize.value) })
      hapticFeedback.selectionChanged()
    }

    const updateTextAlign = (align) => {
      localTextAlign.value = align
      updateElement({ textAlign: align })
      hapticFeedback.impactOccurred('light')
    }

    const toggleBold = () => {
      localFontWeight.value = localFontWeight.value === 'bold' ? 'normal' : 'bold'
      updateElement({ fontWeight: localFontWeight.value })
      hapticFeedback.impactOccurred('light')
    }

    const toggleItalic = () => {
      localFontStyle.value = localFontStyle.value === 'italic' ? 'normal' : 'italic'
      updateElement({ fontStyle: localFontStyle.value })
      hapticFeedback.impactOccurred('light')
    }

    const updateColor = () => {
      updateElement({ color: localColor.value })
      hapticFeedback.selectionChanged()
    }

    const updateStrokeColor = () => {
      updateElement({ strokeColor: localStrokeColor.value })
      hapticFeedback.selectionChanged()
    }

    const updateStrokeWidth = () => {
      updateElement({ strokeWidth: parseInt(localStrokeWidth.value) })
      hapticFeedback.selectionChanged()
    }

    const updateShadowColor = () => {
      updateElement({ shadowColor: localShadowColor.value })
      hapticFeedback.selectionChanged()
    }

    const updateShadowBlur = () => {
      updateElement({ shadowBlur: parseInt(localShadowBlur.value) })
      hapticFeedback.selectionChanged()
    }

    const updateShadowOffsetX = () => {
      updateElement({ shadowOffsetX: parseInt(localShadowOffsetX.value) })
      hapticFeedback.selectionChanged()
    }

    const updateShadowOffsetY = () => {
      updateElement({ shadowOffsetY: parseInt(localShadowOffsetY.value) })
      hapticFeedback.selectionChanged()
    }

    // Position methods
    const applyQuickPosition = (position) => {
      if (!selectedElement.value) return

      const canvasWidth = canvasStore.width
      const canvasHeight = canvasStore.height
      const elementWidth = selectedElement.value.width || 200
      const elementHeight = selectedElement.value.height || 50

      let x = position.x
      let y = position.y

      // Calculate positions
      if (x === 'center') {
        x = (canvasWidth - elementWidth) / 2
      } else if (x === 'right') {
        x = canvasWidth - elementWidth - 50
      }

      if (y === 'center') {
        y = (canvasHeight - elementHeight) / 2
      } else if (y === 'bottom') {
        y = canvasHeight - elementHeight - 50
      }

      updateElement({ x, y })
      hapticFeedback.impactOccurred('medium')
    }

    const updatePosition = (axis, value) => {
      const updates = {}
      updates[axis] = parseInt(value)
      updateElement(updates)
      hapticFeedback.selectionChanged()
    }

    // Toggle collapse
    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
      hapticFeedback.impactOccurred('light')
    }

    return {
      selectedElement,
      isCollapsed,
      activeTab,
      tabs,
      quickPositions,
      toggleCollapse,
      localText,
      localFontFamily,
      localFontSize,
      localTextAlign,
      localFontWeight,
      localFontStyle,
      localColor,
      localStrokeColor,
      localStrokeWidth,
      localShadowColor,
      localShadowBlur,
      localShadowOffsetX,
      localShadowOffsetY,
      updateText,
      updateFontFamily,
      updateFontSize,
      updateTextAlign,
      toggleBold,
      toggleItalic,
      updateColor,
      updateStrokeColor,
      updateStrokeWidth,
      updateShadowColor,
      updateShadowBlur,
      updateShadowOffsetX,
      updateShadowOffsetY,
      applyQuickPosition,
      updatePosition,
    }
  },
}
</script>

<style lang="scss" scoped>
.text-editor-tabs {
  background-color: var(--tg-theme-secondary-bg-color, #ffffff);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.06));

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid var(--tg-theme-hint-color, #e5e5e7);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &-content {
      display: flex;
      align-items: center;
      gap: 12px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--tg-theme-text-color, #000000);
      }
    }

    &-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  &__icon {
    color: var(--tg-theme-link-color, #007aff);
    flex-shrink: 0;
  }

  &__toggle {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 12px;
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
    }

    &.collapsed {
      transform: rotate(-90deg);
    }
  }

  &__close {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 12px;
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: #ff3b30;
      color: white;
      transform: scale(1.05);
    }
  }

  &__content {
    max-height: 70vh;
    overflow-y: auto;

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.2));
      border-radius: 3px;

      &:hover {
        background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.3));
      }
    }
  }

  &__nav {
    display: flex;
    background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
    margin: 16px 20px 0;
    border-radius: 12px;
    padding: 4px;
  }

  &__body {
    padding: 20px;
  }
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background-color: transparent;
  color: var(--tg-theme-hint-color, #8e8e93);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 44px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  &.active {
    color: var(--tg-theme-button-text-color, #ffffff);
    background-color: var(--tg-theme-link-color, #007aff);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    transform: translateY(-1px);
  }
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.field-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--tg-theme-text-color, #000000);
    font-size: 14px;
  }
}

// Input styles
.text-input,
.font-select,
.color-input,
.coord-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  border-radius: 12px;
  background-color: var(--tg-theme-bg-color, #ffffff);
  color: var(--tg-theme-text-color, #000000);
  font-size: 14px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:focus {
    outline: none;
    border-color: var(--tg-theme-link-color, #007aff);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    transform: translateY(-1px);
  }

  &:hover {
    border-color: var(--tg-theme-link-color, rgba(0, 122, 255, 0.3));
  }
}

.text-input {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.size-control {
  display: flex;
  gap: 12px;
  align-items: center;
}

.size-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  border: none;

  &::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--tg-theme-link-color, #007aff);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
    }
  }

  &::-webkit-slider-track {
    height: 6px;
    border-radius: 3px;
    background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  }
}

.size-input {
  width: 80px;
  text-align: center;
}

.font-preview {
  margin-top: 16px;
  padding: 20px;
  background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
  border-radius: 12px;
  border: 2px dashed var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
}

.preview-text {
  text-align: center;
  word-break: break-word;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.style-buttons,
.align-buttons {
  display: flex;
  gap: 8px;
  background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
  padding: 4px;
  border-radius: 12px;
}

.style-btn,
.align-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background-color: transparent;
  color: var(--tg-theme-text-color, #000000);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  &.active {
    background-color: var(--tg-theme-button-color, #007aff);
    color: var(--tg-theme-button-text-color, #ffffff);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    transform: translateY(-1px);
  }
}

.color-control {
  display: flex;
  gap: 12px;
  align-items: center;
}

.color-picker {
  width: 50px !important;
  height: 50px;
  padding: 0 !important;
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  border-radius: 12px;
  cursor: pointer;

  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  &::-webkit-color-swatch {
    border: none;
    border-radius: 10px;
  }
}

.color-input {
  flex: 1;
  font-family: monospace;
}

.stroke-controls,
.shadow-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stroke-width,
.shadow-params {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-control {
  display: flex;
  align-items: center;
  gap: 12px;

  label {
    min-width: 80px;
    margin-bottom: 0;
    font-size: 12px;
  }

  input[type='range'] {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
    border: none;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: var(--tg-theme-link-color, #007aff);
      cursor: pointer;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
  }
}

.value-display {
  font-size: 12px;
  color: var(--tg-theme-hint-color, #8e8e93);
  min-width: 40px;
  text-align: right;
  font-weight: 600;
  background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
  padding: 4px 8px;
  border-radius: 6px;
}

.position-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.position-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  border-radius: 12px;
  background-color: var(--tg-theme-bg-color, #ffffff);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    border-color: var(--tg-theme-link-color, #007aff);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
  }

  span {
    font-size: 10px;
    color: var(--tg-theme-hint-color, #8e8e93);
    text-align: center;
  }
}

.precise-controls {
  display: flex;
  gap: 16px;
}

.coord-control {
  flex: 1;

  label {
    margin-bottom: 8px;
    font-size: 12px;
  }
}

// Collapse animation
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.collapse-enter-to,
.collapse-leave-from {
  max-height: 1000px;
  opacity: 1;
  transform: translateY(0);
}

// Mobile optimizations
@media (max-width: 768px) {
  .text-editor-tabs {
    &__header {
      padding: 16px;
    }

    &__body {
      padding: 16px;
    }

    &__nav {
      margin: 12px 16px 0;
    }
  }

  .tab-button {
    padding: 10px 12px;
    font-size: 12px;

    span {
      display: none;
    }
  }

  .position-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }

  .position-btn {
    padding: 8px 4px;

    span {
      font-size: 8px;
    }
  }

  .precise-controls {
    flex-direction: column;
    gap: 12px;
  }

  .color-control {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .size-control {
    flex-direction: column;
    gap: 8px;
  }
}
</style>

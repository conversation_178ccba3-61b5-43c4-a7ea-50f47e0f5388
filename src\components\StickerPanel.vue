<template>
  <div class="sticker-panel" v-if="isVisible">
    <div class="sticker-panel__header" @click="toggleCollapse">
      <div class="sticker-panel__header-content">
        <Sticker class="sticker-panel__icon" :size="20" />
        <h3>Стикеры и эмодзи</h3>
      </div>
      <div class="sticker-panel__header-actions">
        <button
          class="sticker-panel__toggle"
          @click.stop="toggleCollapse"
          :class="{ collapsed: isCollapsed }"
        >
          <ChevronDown :size="20" />
        </button>
        <button class="sticker-panel__close" @click="$emit('close')">
          <X :size="18" />
        </button>
      </div>
    </div>

    <Transition name="collapse">
      <div class="sticker-panel__body" v-show="!isCollapsed">
        <div class="sticker-panel__tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            :class="['sticker-panel__tab', { active: activeTab === tab.id }]"
            @click="setActiveTab(tab.id)"
          >
            {{ tab.icon }} {{ tab.name }}
          </button>
        </div>

        <div class="sticker-panel__content">
          <!-- Emoji tab -->
          <div v-if="activeTab === 'emoji'" class="sticker-panel__grid">
            <button
              v-for="emoji in emojiList"
              :key="emoji"
              class="sticker-panel__item"
              @click="addEmoji(emoji)"
            >
              {{ emoji }}
            </button>
          </div>

          <!-- Shapes tab -->
          <div v-if="activeTab === 'shapes'" class="sticker-panel__grid">
            <button
              v-for="shape in shapesList"
              :key="shape.name"
              class="sticker-panel__item sticker-panel__item--shape"
              @click="addShape(shape)"
              :title="shape.name"
            >
              <div :style="{ backgroundColor: shape.color }" class="shape-preview">
                {{ shape.icon }}
              </div>
            </button>
          </div>
        </div>

        <!-- Upload tab -->
        <div v-if="activeTab === 'upload'" class="sticker-panel__upload">
          <div class="sticker-panel__upload-area" @click="triggerFileUpload">
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileUpload"
              style="display: none"
            />
            <div class="sticker-panel__upload-icon">📁</div>
            <p>Нажмите для загрузки изображения</p>
            <small>PNG, JPG, GIF до 5MB</small>
          </div>

          <!-- Recent uploads -->
          <div v-if="recentUploads.length > 0" class="sticker-panel__recent">
            <h4>Недавние загрузки</h4>
            <div class="sticker-panel__grid">
              <button
                v-for="upload in recentUploads"
                :key="upload.id"
                class="sticker-panel__item"
                @click="addUploadedImage(upload)"
              >
                <img :src="upload.thumbnail" :alt="upload.name" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import { Sticker, X, ChevronDown } from 'lucide-vue-next'

export default {
  name: 'StickerPanel',
  components: {
    Sticker,
    X,
    ChevronDown,
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['close'],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { hapticFeedback, showAlert } = useTelegram()

    const fileInput = ref(null)
    const activeTab = ref('emoji')
    const recentUploads = ref([])
    const isCollapsed = ref(false)

    const tabs = [
      { id: 'emoji', name: 'Эмодзи', icon: '😀' },
      { id: 'shapes', name: 'Фигуры', icon: '🔷' },
      { id: 'upload', name: 'Загрузить', icon: '📁' },
    ]

    // Emoji categories
    const emojiList = [
      // Smileys
      '😀',
      '😃',
      '😄',
      '😁',
      '😆',
      '😅',
      '😂',
      '🤣',
      '😊',
      '😇',
      '🙂',
      '🙃',
      '😉',
      '😌',
      '😍',
      '🥰',
      '😘',
      '😗',
      '😙',
      '😚',
      '😋',
      '😛',
      '😝',
      '😜',
      '🤪',
      '🤨',
      '🧐',
      '🤓',
      '😎',
      '🤩',
      '🥳',
      '😏',
      '😒',
      '😞',
      '😔',
      '😟',
      '😕',
      '🙁',
      '☹️',
      '😣',
      '😖',
      '😫',
      '😩',
      '🥺',
      '😢',
      '😭',
      '😤',
      '😠',
      '😡',
      '🤬',

      // Hearts
      '❤️',
      '🧡',
      '💛',
      '💚',
      '💙',
      '💜',
      '🖤',
      '🤍',
      '🤎',
      '💔',
      '❣️',
      '💕',
      '💞',
      '💓',
      '💗',
      '💖',
      '💘',
      '💝',

      // Hands
      '👍',
      '👎',
      '👌',
      '🤌',
      '🤏',
      '✌️',
      '🤞',
      '🤟',
      '🤘',
      '🤙',
      '👈',
      '👉',
      '👆',
      '🖕',
      '👇',
      '☝️',
      '👋',
      '🤚',
      '🖐️',
      '✋',
      '🖖',
      '👏',
      '🙌',
      '🤲',
      '🤝',
      '🙏',

      // Objects
      '⭐',
      '🌟',
      '✨',
      '💫',
      '🔥',
      '💥',
      '💢',
      '💨',
      '💦',
      '💤',
      '🎉',
      '🎊',
      '🎈',
      '🎁',
      '🏆',
      '🥇',
      '🥈',
      '🥉',
      '🏅',
      '🎖️',
    ]

    // Predefined shapes
    const shapesList = [
      { name: 'Круг', icon: '●', color: '#007aff', type: 'circle' },
      { name: 'Квадрат', icon: '■', color: '#34c759', type: 'square' },
      { name: 'Треугольник', icon: '▲', color: '#ff9500', type: 'triangle' },
      { name: 'Звезда', icon: '★', color: '#ff3b30', type: 'star' },
      { name: 'Сердце', icon: '♥', color: '#ff2d92', type: 'heart' },
      { name: 'Ромб', icon: '♦', color: '#5856d6', type: 'diamond' },
    ]

    const setActiveTab = (tabId) => {
      activeTab.value = tabId
      hapticFeedback.selectionChanged()
    }

    const addEmoji = (emoji) => {
      const element = {
        type: 'sticker',
        stickerType: 'emoji',
        content: emoji,
        x: 50,
        y: 50,
        width: 80,
        height: 80,
        opacity: 1,
        rotation: 0,
      }

      canvasStore.addElement(element)
      hapticFeedback.impactOccurred('medium')
      emit('close')
    }

    const addShape = (shape) => {
      // Create a canvas to render shape
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const size = 100

      canvas.width = size
      canvas.height = size

      ctx.fillStyle = shape.color

      switch (shape.type) {
        case 'circle':
          ctx.beginPath()
          ctx.arc(size / 2, size / 2, size / 2 - 5, 0, 2 * Math.PI)
          ctx.fill()
          break
        case 'square':
          ctx.fillRect(5, 5, size - 10, size - 10)
          break
        case 'triangle':
          ctx.beginPath()
          ctx.moveTo(size / 2, 5)
          ctx.lineTo(5, size - 5)
          ctx.lineTo(size - 5, size - 5)
          ctx.closePath()
          ctx.fill()
          break
        case 'star':
          drawStar(ctx, size / 2, size / 2, 5, size / 2 - 5, size / 4)
          ctx.fill()
          break
        case 'heart':
          drawHeart(ctx, size / 2, size / 2, size / 3)
          ctx.fill()
          break
        case 'diamond':
          ctx.beginPath()
          ctx.moveTo(size / 2, 5)
          ctx.lineTo(size - 5, size / 2)
          ctx.lineTo(size / 2, size - 5)
          ctx.lineTo(5, size / 2)
          ctx.closePath()
          ctx.fill()
          break
      }

      // Convert to image
      canvas.toBlob((blob) => {
        const img = new Image()
        img.onload = () => {
          canvasStore.addStickerElement({
            image: img,
            x: 50,
            y: 50,
            width: size,
            height: size,
          })
          hapticFeedback.impactOccurred('medium')
        }
        img.src = URL.createObjectURL(blob)
      })
    }

    const drawStar = (ctx, cx, cy, spikes, outerRadius, innerRadius) => {
      let rot = (Math.PI / 2) * 3
      let x = cx
      let y = cy
      const step = Math.PI / spikes

      ctx.beginPath()
      ctx.moveTo(cx, cy - outerRadius)

      for (let i = 0; i < spikes; i++) {
        x = cx + Math.cos(rot) * outerRadius
        y = cy + Math.sin(rot) * outerRadius
        ctx.lineTo(x, y)
        rot += step

        x = cx + Math.cos(rot) * innerRadius
        y = cy + Math.sin(rot) * innerRadius
        ctx.lineTo(x, y)
        rot += step
      }

      ctx.lineTo(cx, cy - outerRadius)
      ctx.closePath()
    }

    const drawHeart = (ctx, x, y, size) => {
      ctx.beginPath()
      ctx.moveTo(x, y + size / 4)
      ctx.quadraticCurveTo(x, y, x - size / 2, y)
      ctx.quadraticCurveTo(x - size, y, x - size, y + size / 4)
      ctx.quadraticCurveTo(x - size, y + size / 2, x - size / 2, y + (size * 3) / 4)
      ctx.lineTo(x, y + size)
      ctx.lineTo(x + size / 2, y + (size * 3) / 4)
      ctx.quadraticCurveTo(x + size, y + size / 2, x + size, y + size / 4)
      ctx.quadraticCurveTo(x + size, y, x + size / 2, y)
      ctx.quadraticCurveTo(x, y, x, y + size / 4)
    }

    const triggerFileUpload = () => {
      fileInput.value?.click()
      hapticFeedback.impactOccurred('light')
    }

    const handleFileUpload = (event) => {
      const file = event.target.files[0]
      if (!file) return

      // Validate file
      if (file.size > 5 * 1024 * 1024) {
        showAlert('Файл слишком большой. Максимальный размер: 5MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        showAlert('Пожалуйста, выберите изображение')
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.onload = () => {
          // Calculate optimal size
          const maxSize = 200
          let { width, height } = img

          if (width > maxSize || height > maxSize) {
            const ratio = Math.min(maxSize / width, maxSize / height)
            width *= ratio
            height *= ratio
          }

          // Add to canvas
          canvasStore.addStickerElement({
            image: img,
            x: 50,
            y: 50,
            width: Math.round(width),
            height: Math.round(height),
          })

          // Add to recent uploads
          const upload = {
            id: Date.now().toString(),
            name: file.name,
            image: img,
            thumbnail: e.target.result,
            width: Math.round(width),
            height: Math.round(height),
          }

          recentUploads.value.unshift(upload)
          if (recentUploads.value.length > 10) {
            recentUploads.value.pop()
          }

          hapticFeedback.impactOccurred('medium')
        }
        img.src = e.target.result
      }
      reader.readAsDataURL(file)

      // Clear input
      event.target.value = ''
    }

    const addUploadedImage = (upload) => {
      canvasStore.addStickerElement({
        image: upload.image,
        x: 50,
        y: 50,
        width: upload.width,
        height: upload.height,
      })
      hapticFeedback.impactOccurred('medium')
    }

    // Toggle collapse
    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
      hapticFeedback.impactOccurred('light')
    }

    return {
      fileInput,
      activeTab,
      tabs,
      emojiList,
      shapesList,
      recentUploads,
      isCollapsed,
      toggleCollapse,
      setActiveTab,
      addEmoji,
      addShape,
      triggerFileUpload,
      handleFileUpload,
      addUploadedImage,
    }
  },
}
</script>

<style lang="scss" scoped>
.sticker-panel {
  background-color: var(--tg-theme-secondary-bg-color, #ffffff);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.06));

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid var(--tg-theme-hint-color, #e5e5e7);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &-content {
      display: flex;
      align-items: center;
      gap: 12px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--tg-theme-text-color, #000000);
      }
    }

    &-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  &__icon {
    color: var(--tg-theme-link-color, #007aff);
    flex-shrink: 0;
  }

  &__toggle {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 12px;
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
    }

    &.collapsed {
      transform: rotate(-90deg);
    }
  }

  &__close {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 12px;
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: #ff3b30;
      color: white;
      transform: scale(1.05);
    }
  }

  &__body {
    max-height: 60vh;
    overflow-y: auto;

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.2));
      border-radius: 3px;

      &:hover {
        background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.3));
      }
    }
  }

  &__tabs {
    display: flex;
    background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
    margin: 16px 20px;
    border-radius: 12px;
    padding: 4px;
  }

  &__tab {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    background-color: transparent;
    color: var(--tg-theme-hint-color, #8e8e93);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.06);
      transform: translateY(-1px);
    }

    &.active {
      color: var(--tg-theme-button-text-color, #ffffff);
      background-color: var(--tg-theme-link-color, #007aff);
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
      transform: translateY(-1px);
    }
  }

  &__content {
    padding: 20px;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 12px;
  }

  &__item {
    aspect-ratio: 1;
    border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
    border-radius: 12px;
    background-color: var(--tg-theme-bg-color, #ffffff);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: var(--tg-theme-link-color, #007aff);
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }

    &--shape {
      .shape-preview {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
      }
    }
  }

  &__upload {
    text-align: center;
  }

  &__upload-area {
    border: 2px dashed var(--tg-theme-hint-color, #c6c6c8);
    border-radius: 12px;
    padding: 32px 16px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--tg-theme-link-color, #007aff);
      background-color: rgba(0, 122, 255, 0.05);
    }
  }

  &__upload-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 0 0 8px 0;
    color: var(--tg-theme-text-color, #000000);
    font-weight: 500;
  }

  small {
    color: var(--tg-theme-hint-color, #8e8e93);
    font-size: 12px;
  }

  &__recent {
    margin-top: 24px;
    text-align: left;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--tg-theme-text-color, #000000);
    }
  }
}

// Collapse animation
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.collapse-enter-to,
.collapse-leave-from {
  max-height: 1000px;
  opacity: 1;
  transform: translateY(0);
}

// Smooth animations for all interactive elements
* {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Enhanced focus styles
button:focus-visible {
  outline: 2px solid var(--tg-theme-link-color, #007aff);
  outline-offset: 2px;
}
</style>

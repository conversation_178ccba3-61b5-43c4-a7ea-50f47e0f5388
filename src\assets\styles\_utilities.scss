// Utility classes

// Spacing utilities
.m-0 { margin: 0; }
.m-1 { margin: $spacing-xs; }
.m-2 { margin: $spacing-sm; }
.m-3 { margin: $spacing-md; }
.m-4 { margin: $spacing-lg; }
.m-5 { margin: $spacing-xl; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: $spacing-xs; }
.mt-2 { margin-top: $spacing-sm; }
.mt-3 { margin-top: $spacing-md; }
.mt-4 { margin-top: $spacing-lg; }
.mt-5 { margin-top: $spacing-xl; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: $spacing-xs; }
.mb-2 { margin-bottom: $spacing-sm; }
.mb-3 { margin-bottom: $spacing-md; }
.mb-4 { margin-bottom: $spacing-lg; }
.mb-5 { margin-bottom: $spacing-xl; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: $spacing-xs; }
.ml-2 { margin-left: $spacing-sm; }
.ml-3 { margin-left: $spacing-md; }
.ml-4 { margin-left: $spacing-lg; }
.ml-5 { margin-left: $spacing-xl; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: $spacing-xs; }
.mr-2 { margin-right: $spacing-sm; }
.mr-3 { margin-right: $spacing-md; }
.mr-4 { margin-right: $spacing-lg; }
.mr-5 { margin-right: $spacing-xl; }

.p-0 { padding: 0; }
.p-1 { padding: $spacing-xs; }
.p-2 { padding: $spacing-sm; }
.p-3 { padding: $spacing-md; }
.p-4 { padding: $spacing-lg; }
.p-5 { padding: $spacing-xl; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: $spacing-xs; }
.pt-2 { padding-top: $spacing-sm; }
.pt-3 { padding-top: $spacing-md; }
.pt-4 { padding-top: $spacing-lg; }
.pt-5 { padding-top: $spacing-xl; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: $spacing-xs; }
.pb-2 { padding-bottom: $spacing-sm; }
.pb-3 { padding-bottom: $spacing-md; }
.pb-4 { padding-bottom: $spacing-lg; }
.pb-5 { padding-bottom: $spacing-xl; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: $spacing-xs; }
.pl-2 { padding-left: $spacing-sm; }
.pl-3 { padding-left: $spacing-md; }
.pl-4 { padding-left: $spacing-lg; }
.pl-5 { padding-left: $spacing-xl; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: $spacing-xs; }
.pr-2 { padding-right: $spacing-sm; }
.pr-3 { padding-right: $spacing-md; }
.pr-4 { padding-right: $spacing-lg; }
.pr-5 { padding-right: $spacing-xl; }

// Display utilities
.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

// Flex utilities
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }
.align-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

// Text utilities
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-md { font-size: $font-size-md; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-xxl { font-size: $font-size-xxl; }

.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-bold { font-weight: $font-weight-bold; }

.text-truncate { @include text-truncate; }

// Position utilities
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

// Width and height utilities
.w-100 { width: 100%; }
.h-100 { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

// Border utilities
.border { border: 1px solid $border-color; }
.border-0 { border: none; }
.border-top { border-top: 1px solid $border-color; }
.border-bottom { border-bottom: 1px solid $border-color; }
.border-left { border-left: 1px solid $border-color; }
.border-right { border-right: 1px solid $border-color; }

.rounded { border-radius: $border-radius-md; }
.rounded-sm { border-radius: $border-radius-sm; }
.rounded-lg { border-radius: $border-radius-lg; }
.rounded-xl { border-radius: $border-radius-xl; }
.rounded-full { border-radius: 50%; }

// Shadow utilities
.shadow-sm { box-shadow: $shadow-sm; }
.shadow-md { box-shadow: $shadow-md; }
.shadow-lg { box-shadow: $shadow-lg; }
.shadow-none { box-shadow: none; }

// Overflow utilities
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

// Cursor utilities
.cursor-pointer { cursor: pointer; }
.cursor-grab { cursor: grab; }
.cursor-grabbing { cursor: grabbing; }
.cursor-not-allowed { cursor: not-allowed; }

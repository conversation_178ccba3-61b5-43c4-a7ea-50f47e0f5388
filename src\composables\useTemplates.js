import { ref, computed } from 'vue'

const STORAGE_KEY = 'post-generator-templates'
const MAX_TEMPLATES = 50

// Template structure
/**
 * @typedef {Object} Template
 * @property {string} id - Unique identifier
 * @property {string} name - Template name
 * @property {string} description - Template description
 * @property {string} thumbnail - Base64 thumbnail image
 * @property {Object} data - Canvas data (background, elements)
 * @property {string} createdAt - ISO date string
 * @property {string} updatedAt - ISO date string
 * @property {Array<string>} tags - Template tags for filtering
 * @property {boolean} isDefault - Whether this is a default template
 */

// Reactive state
const templates = ref([])
const isLoading = ref(false)
const error = ref(null)

// Computed
const userTemplates = computed(() => 
  templates.value.filter(t => !t.isDefault)
)

const defaultTemplates = computed(() => 
  templates.value.filter(t => t.isDefault)
)

const templateCount = computed(() => templates.value.length)

// Load templates from localStorage
const loadTemplates = () => {
  try {
    isLoading.value = true
    const stored = localStorage.getItem(STORAGE_KEY)
    const parsed = stored ? JSON.parse(stored) : []
    
    // Add default templates if not present
    const defaults = getDefaultTemplates()
    const combined = [...defaults, ...parsed]
    
    // Remove duplicates by ID
    const unique = combined.reduce((acc, template) => {
      if (!acc.find(t => t.id === template.id)) {
        acc.push(template)
      }
      return acc
    }, [])
    
    templates.value = unique.sort((a, b) => 
      new Date(b.updatedAt) - new Date(a.updatedAt)
    )
    
    error.value = null
  } catch (err) {
    error.value = 'Ошибка загрузки шаблонов'
    console.error('Failed to load templates:', err)
  } finally {
    isLoading.value = false
  }
}

// Save templates to localStorage
const saveTemplates = () => {
  try {
    const userOnly = templates.value.filter(t => !t.isDefault)
    localStorage.setItem(STORAGE_KEY, JSON.stringify(userOnly))
  } catch (err) {
    error.value = 'Ошибка сохранения шаблонов'
    console.error('Failed to save templates:', err)
  }
}

// Create new template
const createTemplate = (name, description, canvasData, thumbnail, tags = []) => {
  const template = {
    id: generateId(),
    name: name.trim(),
    description: description.trim(),
    thumbnail,
    data: JSON.parse(JSON.stringify(canvasData)),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: tags.map(tag => tag.trim().toLowerCase()),
    isDefault: false
  }
  
  // Add to beginning of array
  templates.value.unshift(template)
  
  // Limit number of templates
  if (userTemplates.value.length > MAX_TEMPLATES) {
    const oldestUserTemplate = userTemplates.value[userTemplates.value.length - 1]
    deleteTemplate(oldestUserTemplate.id)
  }
  
  saveTemplates()
  return template
}

// Update existing template
const updateTemplate = (id, updates) => {
  const index = templates.value.findIndex(t => t.id === id)
  if (index === -1) return null
  
  const template = templates.value[index]
  if (template.isDefault) {
    throw new Error('Нельзя изменять стандартные шаблоны')
  }
  
  templates.value[index] = {
    ...template,
    ...updates,
    id, // Preserve ID
    updatedAt: new Date().toISOString(),
    isDefault: false // Preserve user template status
  }
  
  saveTemplates()
  return templates.value[index]
}

// Delete template
const deleteTemplate = (id) => {
  const template = templates.value.find(t => t.id === id)
  if (template?.isDefault) {
    throw new Error('Нельзя удалять стандартные шаблоны')
  }
  
  const index = templates.value.findIndex(t => t.id === id)
  if (index !== -1) {
    templates.value.splice(index, 1)
    saveTemplates()
    return true
  }
  return false
}

// Get template by ID
const getTemplate = (id) => {
  return templates.value.find(t => t.id === id) || null
}

// Search templates
const searchTemplates = (query, tags = []) => {
  const lowerQuery = query.toLowerCase().trim()
  const lowerTags = tags.map(tag => tag.toLowerCase().trim())
  
  return templates.value.filter(template => {
    const matchesQuery = !lowerQuery || 
      template.name.toLowerCase().includes(lowerQuery) ||
      template.description.toLowerCase().includes(lowerQuery)
    
    const matchesTags = lowerTags.length === 0 ||
      lowerTags.some(tag => template.tags.includes(tag))
    
    return matchesQuery && matchesTags
  })
}

// Generate thumbnail from canvas
const generateThumbnail = (canvasElement, maxWidth = 200, maxHeight = 150) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    // Calculate thumbnail dimensions
    const aspectRatio = canvasElement.width / canvasElement.height
    let thumbWidth = maxWidth
    let thumbHeight = maxWidth / aspectRatio
    
    if (thumbHeight > maxHeight) {
      thumbHeight = maxHeight
      thumbWidth = maxHeight * aspectRatio
    }
    
    canvas.width = thumbWidth
    canvas.height = thumbHeight
    
    // Draw scaled canvas
    ctx.drawImage(canvasElement, 0, 0, thumbWidth, thumbHeight)
    
    // Convert to base64
    resolve(canvas.toDataURL('image/jpeg', 0.8))
  })
}

// Get all unique tags
const getAllTags = () => {
  const tagSet = new Set()
  templates.value.forEach(template => {
    template.tags.forEach(tag => tagSet.add(tag))
  })
  return Array.from(tagSet).sort()
}

// Generate unique ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// Default templates
const getDefaultTemplates = () => {
  return [
    {
      id: 'default-quote-1',
      name: 'Простая цитата',
      description: 'Минималистичный шаблон для цитат',
      thumbnail: '', // Will be generated
      data: {
        width: 800,
        height: 600,
        background: {
          type: 'gradient',
          colors: ['#667eea', '#764ba2'],
          opacity: 1
        },
        elements: [
          {
            id: 'quote-text',
            type: 'text',
            text: 'Ваша цитата здесь',
            x: 100,
            y: 250,
            width: 600,
            height: 100,
            fontSize: 36,
            fontFamily: 'Georgia, serif',
            color: '#ffffff',
            textAlign: 'center',
            fontWeight: 'normal',
            fontStyle: 'italic',
            strokeColor: '',
            strokeWidth: 0,
            shadowColor: 'rgba(0,0,0,0.3)',
            shadowBlur: 4,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            rotation: 0,
            scaleX: 1,
            scaleY: 1,
            opacity: 1,
            zIndex: 1
          }
        ]
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      tags: ['цитата', 'градиент', 'минимализм'],
      isDefault: true
    },
    {
      id: 'default-announcement-1',
      name: 'Объявление',
      description: 'Яркий шаблон для объявлений',
      thumbnail: '',
      data: {
        width: 800,
        height: 600,
        background: {
          type: 'color',
          value: '#ff6b6b',
          opacity: 1
        },
        elements: [
          {
            id: 'title',
            type: 'text',
            text: 'ВАЖНО!',
            x: 50,
            y: 100,
            width: 700,
            height: 80,
            fontSize: 64,
            fontFamily: 'Impact, sans-serif',
            color: '#ffffff',
            textAlign: 'center',
            fontWeight: 'bold',
            fontStyle: 'normal',
            strokeColor: '#000000',
            strokeWidth: 2,
            shadowColor: '',
            shadowBlur: 0,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            rotation: 0,
            scaleX: 1,
            scaleY: 1,
            opacity: 1,
            zIndex: 1
          },
          {
            id: 'content',
            type: 'text',
            text: 'Ваш текст объявления',
            x: 100,
            y: 300,
            width: 600,
            height: 200,
            fontSize: 28,
            fontFamily: 'Arial, sans-serif',
            color: '#ffffff',
            textAlign: 'center',
            fontWeight: 'normal',
            fontStyle: 'normal',
            strokeColor: '',
            strokeWidth: 0,
            shadowColor: 'rgba(0,0,0,0.5)',
            shadowBlur: 2,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            rotation: 0,
            scaleX: 1,
            scaleY: 1,
            opacity: 1,
            zIndex: 2
          }
        ]
      },
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      tags: ['объявление', 'яркий', 'важно'],
      isDefault: true
    }
  ]
}

// Composable
export function useTemplates() {
  // Load templates on first use
  if (templates.value.length === 0) {
    loadTemplates()
  }
  
  return {
    // State
    templates,
    userTemplates,
    defaultTemplates,
    templateCount,
    isLoading,
    error,
    
    // Methods
    loadTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    getTemplate,
    searchTemplates,
    generateThumbnail,
    getAllTags
  }
}

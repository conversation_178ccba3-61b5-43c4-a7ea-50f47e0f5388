# 🧪 Тестирование Post Generator

Руководство по тестированию конструктора визуальных Telegram-постов.

## 🚀 Быстрый запуск для тестирования

### 1. Запуск Frontend

```bash
npm install
npm run dev
```

Приложение будет доступно по адресу: http://localhost:5173

### 2. Тестирование без Telegram

Откройте браузер и перейдите по адресу выше. Приложение будет работать в режиме разработки с моковыми данными пользователя.

### 3. Тестирование с Telegram WebApp

#### Настройка ngrok (для локального тестирования)

```bash
# Установите ngrok
npm install -g ngrok

# Запустите туннель
ngrok http 5173
```

Скопируйте HTTPS URL из ngrok (например: `https://abc123.ngrok.io`)

#### Настройка бота в BotFather

1. Создайте бота у [@BotFather](https://t.me/BotFather):
   ```
   /newbot
   Название: Post Generator Test
   Username: your_test_bot
   ```

2. Настройте WebApp:
   ```
   /setmenubutton
   @your_test_bot
   🎨 Создать пост
   https://abc123.ngrok.io
   ```

3. Включите inline режим:
   ```
   /setinline
   @your_test_bot
   Создание постов с цитатами
   ```

### 4. Запуск бота (опционально)

```bash
cd bot
cp .env.example .env
# Отредактируйте .env файл с токеном бота
npm install
npm run dev
```

## 🧪 Сценарии тестирования

### Frontend (WebApp)

#### ✅ Базовая функциональность

1. **Загрузка приложения**
   - [ ] Приложение загружается без ошибок
   - [ ] Отображается интерфейс редактора
   - [ ] Toolbar содержит все кнопки

2. **Добавление текста**
   - [ ] Кнопка "T" добавляет новый текстовый элемент
   - [ ] Текст можно редактировать в панели
   - [ ] Изменения применяются в реальном времени
   - [ ] Можно изменить шрифт, размер, цвет

3. **Работа со стикерами**
   - [ ] Панель стикеров открывается
   - [ ] Эмодзи добавляются на canvas
   - [ ] Фигуры создаются корректно
   - [ ] Загрузка изображений работает

4. **Drag & Drop**
   - [ ] Элементы можно перетаскивать
   - [ ] Выделение элементов работает
   - [ ] Контекстные действия доступны

#### ✅ Продвинутые функции

5. **Настройка текста**
   - [ ] Изменение шрифта
   - [ ] Настройка размера (12-120px)
   - [ ] Выравнивание (лево/центр/право)
   - [ ] Жирный/курсив
   - [ ] Цвет текста
   - [ ] Обводка (цвет и толщина)
   - [ ] Тень (цвет, размытие, смещение)

6. **Управление элементами**
   - [ ] Дублирование элементов
   - [ ] Удаление элементов
   - [ ] Перемещение на передний/задний план
   - [ ] Undo/Redo операции

7. **Шаблоны**
   - [ ] Сохранение шаблона
   - [ ] Загрузка шаблона
   - [ ] Поиск по шаблонам
   - [ ] Удаление пользовательских шаблонов

8. **Экспорт**
   - [ ] Экспорт в PNG
   - [ ] Качество изображения
   - [ ] Корректные размеры

#### ✅ Мобильная адаптация

9. **Responsive дизайн**
   - [ ] Интерфейс адаптируется под мобильные
   - [ ] Touch-friendly элементы (44px+)
   - [ ] Горизонтальная ориентация
   - [ ] Очень маленькие экраны (iPhone SE)

10. **Telegram интеграция**
    - [ ] Haptic feedback работает
    - [ ] Темы Telegram применяются
    - [ ] Main Button отображается
    - [ ] Данные пользователя получаются

### Backend (Inline Bot)

#### ✅ Inline запросы

11. **Базовые inline запросы**
    - [ ] Пустой запрос показывает помощь
    - [ ] Текстовый запрос генерирует изображения
    - [ ] 4 шаблона возвращаются
    - [ ] Изображения корректно отображаются

12. **Генерация изображений**
    - [ ] Градиентные фоны
    - [ ] Сплошные цвета
    - [ ] Перенос текста по словам
    - [ ] Центрирование текста
    - [ ] Тени и эффекты

#### ✅ Команды бота

13. **Команда /start**
    - [ ] Приветственное сообщение
    - [ ] Кнопка WebApp
    - [ ] Inline кнопка с примером
    - [ ] Кнопки помощи и настроек

14. **Callback запросы**
    - [ ] Помощь отображается
    - [ ] Настройки показываются
    - [ ] Возврат в главное меню

## 🐛 Известные проблемы и ограничения

### Frontend
- Canvas может работать медленно на старых устройствах
- Большие изображения могут вызвать проблемы с памятью
- Некоторые шрифты могут не загружаться

### Bot
- Inline изображения имеют ограничения по размеру
- Canvas требует дополнительных зависимостей на сервере
- Генерация изображений может быть медленной

## 🔧 Отладка

### Frontend

```bash
# Проверка консоли браузера
F12 -> Console

# Проверка Telegram WebApp
window.Telegram.WebApp

# Проверка состояния приложения
Vue DevTools
```

### Bot

```bash
# Логи бота
npm run dev

# Проверка webhook (для продакшена)
curl -X GET "https://api.telegram.org/bot<TOKEN>/getWebhookInfo"
```

## 📊 Метрики производительности

### Целевые показатели

- **Время загрузки**: < 3 секунды
- **Размер bundle**: < 1MB
- **FCP (First Contentful Paint)**: < 2 секунды
- **LCP (Largest Contentful Paint)**: < 4 секунды
- **CLS (Cumulative Layout Shift)**: < 0.1

### Проверка производительности

```bash
# Lighthouse audit
npm run build
npx lighthouse http://localhost:4173 --view

# Bundle analyzer
npm run build -- --analyze
```

## ✅ Чек-лист перед релизом

### Frontend
- [ ] Все компоненты работают без ошибок
- [ ] Мобильная версия протестирована
- [ ] Telegram WebApp интеграция работает
- [ ] Переводы на RU/EN корректны
- [ ] Performance audit пройден

### Bot
- [ ] Inline запросы работают
- [ ] Команды отвечают корректно
- [ ] Изображения генерируются
- [ ] Webhook настроен (для продакшена)

### Деплой
- [ ] Frontend деплоится на Vercel
- [ ] Bot деплоится на Railway/Heroku
- [ ] Переменные окружения настроены
- [ ] HTTPS сертификаты валидны

## 🆘 Поддержка

При обнаружении багов:

1. Проверьте консоль браузера/логи сервера
2. Воспроизведите проблему
3. Создайте issue с подробным описанием
4. Приложите скриншоты/логи

---

**Удачного тестирования! 🚀**

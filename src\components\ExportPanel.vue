<template>
  <div class="export-panel">
    <div class="export-options">
      <!-- Quick Export -->
      <div class="quick-export">
        <h4>Быстрый экспорт</h4>
        <div class="quick-buttons">
          <button class="export-btn" @click="quickExport('png')">
            <FileImage :size="20" />
            <span>PNG</span>
          </button>
          <button class="export-btn" @click="quickExport('jpg')">
            <FileImage :size="20" />
            <span>JPEG</span>
          </button>
          <button class="export-btn" @click="quickExport('svg')">
            <FileText :size="20" />
            <span>SVG</span>
          </button>
        </div>
      </div>

      <!-- Advanced Export -->
      <div class="advanced-export">
        <h4>Расширенный экспорт</h4>
        <button class="advanced-btn" @click="openAdvancedExport">
          <Settings :size="20" />
          <span>Настройки экспорта</span>
          <ChevronRight :size="16" />
        </button>
      </div>

      <!-- Share Options -->
      <div class="share-options" v-if="isTelegramWebApp">
        <h4>Поделиться</h4>
        <div class="share-buttons">
          <button class="share-btn" @click="shareToTelegram">
            <Send :size="20" />
            <span>Отправить в Telegram</span>
          </button>
          <button class="share-btn" @click="copyToClipboard">
            <Copy :size="20" />
            <span>Копировать изображение</span>
          </button>
        </div>
      </div>

      <!-- Templates -->
      <div class="template-options">
        <h4>Шаблоны</h4>
        <div class="template-buttons">
          <button class="template-btn" @click="saveAsTemplate">
            <Save :size="20" />
            <span>Сохранить как шаблон</span>
          </button>
          <button class="template-btn" @click="loadTemplate">
            <FolderOpen :size="20" />
            <span>Загрузить шаблон</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Export Modal -->
    <ExportModal
      :is-visible="showExportModal"
      @close="showExportModal = false"
      @export="handleExport"
    />

    <!-- Template Modal -->
    <TemplateModal
      v-if="showTemplateModal"
      :is-visible="showTemplateModal"
      :mode="templateMode"
      @close="showTemplateModal = false"
      @template-saved="handleTemplateSaved"
      @template-loaded="handleTemplateLoaded"
    />
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import {
  FileImage,
  FileText,
  Settings,
  ChevronRight,
  Send,
  Copy,
  Save,
  FolderOpen,
} from 'lucide-vue-next'

import ExportModal from './ExportModal.vue'
import TemplateModal from './TemplateModal.vue'

export default {
  name: 'ExportPanel',
  props: {
    canvasRef: {
      type: Object,
      default: null,
    },
  },
  components: {
    FileImage,
    FileText,
    Settings,
    ChevronRight,
    Send,
    Copy,
    Save,
    FolderOpen,
    ExportModal,
    TemplateModal,
  },
  emits: ['close'],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { webApp, hapticFeedback, showAlert } = useTelegram()

    const showExportModal = ref(false)
    const showTemplateModal = ref(false)
    const templateMode = ref('save')

    const isTelegramWebApp = computed(() => !!webApp.value)

    const quickExport = async (format) => {
      try {
        // Get canvas data
        if (!props.canvasRef?.canvasRef) {
          throw new Error('Canvas not found')
        }

        const dataUrl = props.canvasRef.canvasRef.toDataURL(
          `image/${format === 'jpg' ? 'jpeg' : format}`,
          0.9,
        )

        // Create download link
        const link = document.createElement('a')
        link.download = `framory-design.${format}`
        link.href = dataUrl
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        hapticFeedback.notificationOccurred('success')
        showAlert('Изображение сохранено!')
      } catch (error) {
        console.error('Export failed:', error)
        hapticFeedback.notificationOccurred('error')
        showAlert('Ошибка при экспорте')
      }
    }

    const openAdvancedExport = () => {
      showExportModal.value = true
      hapticFeedback.impactOccurred('light')
    }

    const handleExport = async (exportData) => {
      // Handle advanced export
      console.log('Advanced export:', exportData)
      showExportModal.value = false
      emit('close')
    }

    const shareToTelegram = async () => {
      try {
        const canvas = document.querySelector('canvas')
        if (!canvas) {
          throw new Error('Canvas not found')
        }

        const dataUrl = canvas.toDataURL('image/png', 0.9)

        if (webApp.value) {
          webApp.value.sendData(
            JSON.stringify({
              type: 'photo',
              dataUrl: dataUrl,
              caption: 'Создано в Framory',
            }),
          )
        }

        hapticFeedback.notificationOccurred('success')
      } catch (error) {
        console.error('Share failed:', error)
        hapticFeedback.notificationOccurred('error')
      }
    }

    const copyToClipboard = async () => {
      try {
        const canvas = document.querySelector('canvas')
        if (!canvas) {
          throw new Error('Canvas not found')
        }

        canvas.toBlob(async (blob) => {
          if (blob && navigator.clipboard) {
            await navigator.clipboard.write([new ClipboardItem({ 'image/png': blob })])
            hapticFeedback.notificationOccurred('success')
            showAlert('Изображение скопировано!')
          }
        })
      } catch (error) {
        console.error('Copy failed:', error)
        hapticFeedback.notificationOccurred('error')
        showAlert('Ошибка при копировании')
      }
    }

    const saveAsTemplate = () => {
      templateMode.value = 'save'
      showTemplateModal.value = true
      hapticFeedback.impactOccurred('light')
    }

    const loadTemplate = () => {
      templateMode.value = 'load'
      showTemplateModal.value = true
      hapticFeedback.impactOccurred('light')
    }

    const handleTemplateSaved = (template) => {
      showTemplateModal.value = false
      showAlert('Шаблон сохранен!')
      hapticFeedback.notificationOccurred('success')
    }

    const handleTemplateLoaded = (template) => {
      showTemplateModal.value = false
      showAlert('Шаблон загружен!')
      hapticFeedback.notificationOccurred('success')
      emit('close')
    }

    return {
      showExportModal,
      showTemplateModal,
      templateMode,
      isTelegramWebApp,
      quickExport,
      openAdvancedExport,
      handleExport,
      shareToTelegram,
      copyToClipboard,
      saveAsTemplate,
      loadTemplate,
      handleTemplateSaved,
      handleTemplateLoaded,
    }
  },
}
</script>

<style lang="scss" scoped>
.export-panel {
  padding: 20px;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.quick-export,
.advanced-export,
.share-options,
.template-options {
  h4 {
    margin: 0 0 12px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }
}

.quick-buttons,
.share-buttons,
.template-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.export-btn,
.share-btn,
.template-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    color: #667eea;
    transform: translateY(-2px);
  }
}

.advanced-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    color: #667eea;
  }

  span {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
</style>

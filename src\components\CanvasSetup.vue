<template>
  <div class="canvas-setup">
    <div class="canvas-setup__container">
      <div class="canvas-setup__header">
        <h1 class="canvas-setup__title">
          <Palette :size="32" />
          Создать новый дизайн
        </h1>
        <p class="canvas-setup__subtitle">Выберите размер холста и начните творить</p>
      </div>

      <div class="canvas-setup__content">
        <!-- Preset sizes -->
        <div class="canvas-setup__section">
          <h2 class="canvas-setup__section-title">
            <Sparkles :size="20" />
            Популярные размеры
          </h2>
          <div class="canvas-setup__presets">
            <button
              v-for="preset in presets"
              :key="preset.id"
              class="preset-card"
              :class="{ active: selectedPreset?.id === preset.id }"
              @click="selectPreset(preset)"
            >
              <div class="preset-card__icon">
                <component :is="preset.icon" :size="24" />
              </div>
              <div class="preset-card__info">
                <h3 class="preset-card__name">{{ preset.name }}</h3>
                <p class="preset-card__size">{{ preset.width }} × {{ preset.height }}</p>
                <p class="preset-card__desc">{{ preset.description }}</p>
              </div>
              <div class="preset-card__preview">
                <div
                  class="preset-card__canvas"
                  :style="{
                    aspectRatio: `${preset.width} / ${preset.height}`,
                    backgroundColor: preset.defaultBg,
                  }"
                ></div>
              </div>
            </button>
          </div>
        </div>

        <!-- Custom size -->
        <div class="canvas-setup__section">
          <h2 class="canvas-setup__section-title">
            <Settings :size="20" />
            Пользовательский размер
          </h2>
          <div class="custom-size">
            <div class="custom-size__inputs">
              <div class="input-group">
                <label>Ширина</label>
                <input
                  type="number"
                  v-model="customWidth"
                  min="100"
                  max="4000"
                  @input="selectCustom"
                />
                <span class="unit">px</span>
              </div>
              <div class="input-group">
                <label>Высота</label>
                <input
                  type="number"
                  v-model="customHeight"
                  min="100"
                  max="4000"
                  @input="selectCustom"
                />
                <span class="unit">px</span>
              </div>
            </div>
            <div class="custom-size__preview">
              <div
                class="custom-preview"
                :style="{
                  aspectRatio: `${customWidth} / ${customHeight}`,
                  backgroundColor: selectedBackground,
                }"
              ></div>
            </div>
          </div>
        </div>

        <!-- Background selection -->
        <div class="canvas-setup__section">
          <h2 class="canvas-setup__section-title">
            <Image :size="20" />
            Фон холста
          </h2>
          <div class="background-options">
            <button
              v-for="bg in backgrounds"
              :key="bg.id"
              class="bg-option"
              :class="{ active: selectedBackground === bg.value }"
              @click="selectedBackground = bg.value"
            >
              <div class="bg-option__preview" :style="{ background: bg.value }"></div>
              <span class="bg-option__name">{{ bg.name }}</span>
            </button>
          </div>
        </div>
      </div>

      <div class="canvas-setup__actions">
        <button class="btn btn--secondary" @click="$emit('cancel')">
          <ArrowLeft :size="18" />
          Назад
        </button>
        <button class="btn btn--primary" @click="createCanvas" :disabled="!canCreate">
          <Plus :size="18" />
          Создать холст
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import {
  Palette,
  Sparkles,
  Settings,
  Image,
  ArrowLeft,
  Plus,
  Instagram,
  Youtube,
  Monitor,
  Smartphone,
  FileImage,
  Square,
} from 'lucide-vue-next'

export default {
  name: 'CanvasSetup',
  components: {
    Palette,
    Sparkles,
    Settings,
    Image,
    ArrowLeft,
    Plus,
    Instagram,
    Youtube,
    Monitor,
    Smartphone,
    FileImage,
    Square,
  },
  emits: ['canvas-created', 'cancel'],
  setup(props, { emit }) {
    const selectedPreset = ref(null)
    const customWidth = ref(800)
    const customHeight = ref(600)
    const selectedBackground = ref('#ffffff')

    const presets = [
      {
        id: 'instagram-post',
        name: 'Instagram Post',
        width: 1080,
        height: 1080,
        description: 'Квадратный пост',
        icon: 'Instagram',
        defaultBg: '#ffffff',
      },
      {
        id: 'instagram-story',
        name: 'Instagram Story',
        width: 1080,
        height: 1920,
        description: 'Вертикальная история',
        icon: 'Smartphone',
        defaultBg: '#000000',
      },
      {
        id: 'youtube-thumbnail',
        name: 'YouTube Thumbnail',
        width: 1280,
        height: 720,
        description: 'Превью для видео',
        icon: 'Youtube',
        defaultBg: '#ff0000',
      },
      {
        id: 'facebook-post',
        name: 'Facebook Post',
        width: 1200,
        height: 630,
        description: 'Пост в Facebook',
        icon: 'Monitor',
        defaultBg: '#1877f2',
      },
      {
        id: 'twitter-post',
        name: 'Twitter Post',
        width: 1024,
        height: 512,
        description: 'Пост в Twitter',
        icon: 'FileImage',
        defaultBg: '#1da1f2',
      },
      {
        id: 'square',
        name: 'Квадрат',
        width: 1000,
        height: 1000,
        description: 'Универсальный квадрат',
        icon: 'Square',
        defaultBg: '#f0f0f0',
      },
    ]

    const backgrounds = [
      { id: 'white', name: 'Белый', value: '#ffffff' },
      { id: 'black', name: 'Черный', value: '#000000' },
      { id: 'gray', name: 'Серый', value: '#f5f5f5' },
      { id: 'blue', name: 'Синий', value: '#007aff' },
      { id: 'red', name: 'Красный', value: '#ff3b30' },
      { id: 'green', name: 'Зеленый', value: '#34c759' },
      { id: 'purple', name: 'Фиолетовый', value: '#af52de' },
      {
        id: 'gradient1',
        name: 'Градиент 1',
        value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      },
      {
        id: 'gradient2',
        name: 'Градиент 2',
        value: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      },
      {
        id: 'gradient3',
        name: 'Градиент 3',
        value: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      },
    ]

    const canCreate = computed(() => {
      if (selectedPreset.value) return true
      return customWidth.value >= 100 && customHeight.value >= 100
    })

    const selectPreset = (preset) => {
      selectedPreset.value = preset
      selectedBackground.value = preset.defaultBg
    }

    const selectCustom = () => {
      selectedPreset.value = null
    }

    const createCanvas = () => {
      const config = selectedPreset.value
        ? {
            width: selectedPreset.value.width,
            height: selectedPreset.value.height,
            background: selectedBackground.value,
            preset: selectedPreset.value,
          }
        : {
            width: parseInt(customWidth.value),
            height: parseInt(customHeight.value),
            background: selectedBackground.value,
            preset: null,
          }

      emit('canvas-created', config)
    }

    return {
      selectedPreset,
      customWidth,
      customHeight,
      selectedBackground,
      presets,
      backgrounds,
      canCreate,
      selectPreset,
      selectCustom,
      createCanvas,
    }
  },
}
</script>

<style lang="scss" scoped>
.canvas-setup {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  &__container {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }
  }

  &__header {
    text-align: center;
    padding: 40px 40px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  &__title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin: 0 0 12px;
    font-size: 32px;
    font-weight: 700;
    color: #1a1a1a;
  }

  &__subtitle {
    margin: 0;
    font-size: 16px;
    color: #666;
  }

  &__content {
    padding: 30px 40px;
  }

  &__section {
    margin-bottom: 40px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 20px;
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
  }

  &__presets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px 40px;
    border-top: 1px solid #f0f0f0;
  }
}

.preset-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid #e5e5e5;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;

  &:hover {
    border-color: #007aff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.15);
  }

  &.active {
    border-color: #007aff;
    background: rgba(0, 122, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.2);
  }

  &__icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 12px;
    color: #007aff;
  }

  &__info {
    flex: 1;
    min-width: 0;
  }

  &__name {
    margin: 0 0 4px;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
  }

  &__size {
    margin: 0 0 4px;
    font-size: 14px;
    color: #666;
    font-family: monospace;
  }

  &__desc {
    margin: 0;
    font-size: 12px;
    color: #999;
  }

  &__preview {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 8px;
  }

  &__canvas {
    width: 100%;
    max-width: 44px;
    max-height: 44px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}

.custom-size {
  display: flex;
  gap: 24px;
  align-items: flex-start;

  &__inputs {
    flex: 1;
    display: flex;
    gap: 16px;
  }

  &__preview {
    flex-shrink: 0;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 12px;
  }
}

.custom-preview {
  width: 100%;
  max-width: 96px;
  max-height: 96px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.input-group {
  flex: 1;
  position: relative;

  label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #1a1a1a;
  }

  input {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid #e5e5e5;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: #007aff;
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }
  }

  .unit {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #666;
    pointer-events: none;
    margin-top: 16px;
  }
}

.background-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
}

.bg-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #007aff;
    transform: translateY(-2px);
  }

  &.active {
    border-color: #007aff;
    background: rgba(0, 122, 255, 0.05);
  }

  &__preview {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  &__name {
    font-size: 12px;
    color: #666;
    text-align: center;
  }
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }

  &--primary {
    background: #007aff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056cc;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
    }
  }

  &--secondary {
    background: #f8f9fa;
    color: #666;

    &:hover:not(:disabled) {
      background: #e9ecef;
      transform: translateY(-2px);
    }
  }
}

@media (max-width: 768px) {
  .canvas-setup {
    padding: 12px;

    &__container {
      border-radius: 16px;
    }

    &__header {
      padding: 24px 24px 16px;
    }

    &__title {
      font-size: 24px;
    }

    &__content {
      padding: 20px 24px;
    }

    &__actions {
      padding: 16px 24px 24px;
      flex-direction: column;
      gap: 12px;

      .btn {
        width: 100%;
        justify-content: center;
      }
    }

    &__presets {
      grid-template-columns: 1fr;
    }
  }

  .custom-size {
    flex-direction: column;
    gap: 16px;

    &__inputs {
      flex-direction: column;
    }

    &__preview {
      align-self: center;
    }
  }

  .background-options {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
}
</style>

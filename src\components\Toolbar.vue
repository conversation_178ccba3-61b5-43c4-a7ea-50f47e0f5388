<template>
  <div class="toolbar">
    <div class="toolbar__group">
      <!-- Undo/Redo -->
      <button
        class="toolbar__button"
        :class="{ 'toolbar__button--disabled': !canUndo }"
        :disabled="!canUndo"
        @click="handleUndo"
        title="Отменить"
      >
        <Undo2 :size="18" />
      </button>

      <button
        class="toolbar__button"
        :class="{ 'toolbar__button--disabled': !canRedo }"
        :disabled="!canRedo"
        @click="handleRedo"
        title="Повторить"
      >
        <Redo2 :size="18" />
      </button>
    </div>

    <div class="toolbar__group">
      <!-- Add elements -->
      <button
        class="toolbar__button"
        :class="{ 'toolbar__button--active': activeMode === 'text' }"
        @click="handleAddText"
        title="Добавить текст"
      >
        <Type :size="18" />
      </button>

      <button
        class="toolbar__button"
        :class="{ 'toolbar__button--active': activeMode === 'sticker' }"
        @click="handleToggleStickers"
        title="Добавить стикер"
      >
        <Sticker :size="18" />
      </button>

      <button class="toolbar__button" @click="handleBackgroundSettings" title="Настройки фона">
        <Palette :size="18" />
      </button>
    </div>

    <div class="toolbar__group">
      <!-- Element actions -->
      <button
        v-if="selectedElement"
        class="toolbar__button"
        @click="handleDuplicate"
        title="Дублировать"
      >
        <Copy :size="18" />
      </button>

      <button v-if="selectedElement" class="toolbar__button" @click="handleDelete" title="Удалить">
        <Trash2 :size="18" />
      </button>

      <button
        v-if="selectedElement"
        class="toolbar__button"
        @click="handleMoveToFront"
        title="На передний план"
      >
        <MoveUp :size="18" />
      </button>

      <button
        v-if="selectedElement"
        class="toolbar__button"
        @click="handleMoveToBack"
        title="На задний план"
      >
        <MoveDown :size="18" />
      </button>
    </div>

    <div class="toolbar__group">
      <!-- Canvas actions -->
      <button class="toolbar__button" @click="handleClear" title="Очистить холст">
        <Eraser :size="18" />
      </button>

      <button class="toolbar__button" @click="handleSaveTemplate" title="Сохранить как шаблон">
        <Save :size="18" />
      </button>

      <button
        class="toolbar__button toolbar__button--primary"
        @click="handleExport"
        title="Экспортировать"
      >
        <Download :size="18" />
      </button>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import {
  Undo2,
  Redo2,
  Type,
  Sticker,
  Palette,
  Copy,
  Trash2,
  MoveUp,
  MoveDown,
  Eraser,
  Save,
  Download,
} from 'lucide-vue-next'

export default {
  name: 'Toolbar',
  components: {
    Undo2,
    Redo2,
    Type,
    Sticker,
    Palette,
    Copy,
    Trash2,
    MoveUp,
    MoveDown,
    Eraser,
    Save,
    Download,
  },
  props: {
    activeMode: {
      type: String,
      default: null,
    },
  },
  emits: [
    'mode-changed',
    'add-text',
    'toggle-stickers',
    'background-settings',
    'export',
    'save-template',
  ],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { hapticFeedback, showConfirm, showAlert } = useTelegram()

    const selectedElement = computed(() => canvasStore.selectedElement)
    const canUndo = computed(() => canvasStore.canUndo)
    const canRedo = computed(() => canvasStore.canRedo)

    const handleUndo = () => {
      canvasStore.undo()
      hapticFeedback.impactOccurred('light')
    }

    const handleRedo = () => {
      canvasStore.redo()
      hapticFeedback.impactOccurred('light')
    }

    const handleAddText = () => {
      const newMode = props.activeMode === 'text' ? null : 'text'
      emit('mode-changed', newMode)

      if (newMode === 'text') {
        emit('add-text')
        hapticFeedback.impactOccurred('medium')
      }
    }

    const handleToggleStickers = () => {
      const newMode = props.activeMode === 'sticker' ? null : 'sticker'
      emit('mode-changed', newMode)
      emit('toggle-stickers')
      hapticFeedback.impactOccurred('light')
    }

    const handleBackgroundSettings = () => {
      emit('background-settings')
      hapticFeedback.impactOccurred('light')
    }

    const handleDuplicate = () => {
      if (selectedElement.value) {
        canvasStore.duplicateElement(selectedElement.value.id)
        hapticFeedback.impactOccurred('medium')
      }
    }

    const handleDelete = async () => {
      if (selectedElement.value) {
        const confirmed = await showConfirm('Удалить выбранный элемент?')
        if (confirmed) {
          canvasStore.deleteElement(selectedElement.value.id)
          hapticFeedback.impactOccurred('heavy')
        }
      }
    }

    const handleMoveToFront = () => {
      if (selectedElement.value) {
        canvasStore.moveElementToFront(selectedElement.value.id)
        hapticFeedback.impactOccurred('light')
      }
    }

    const handleMoveToBack = () => {
      if (selectedElement.value) {
        canvasStore.moveElementToBack(selectedElement.value.id)
        hapticFeedback.impactOccurred('light')
      }
    }

    const handleClear = async () => {
      const confirmed = await showConfirm('Очистить весь холст? Это действие нельзя отменить.')
      if (confirmed) {
        canvasStore.clear()
        hapticFeedback.impactOccurred('heavy')
      }
    }

    const handleSaveTemplate = () => {
      emit('save-template')
      hapticFeedback.impactOccurred('medium')
    }

    const handleExport = () => {
      emit('export')
      hapticFeedback.impactOccurred('medium')
    }

    return {
      selectedElement,
      canUndo,
      canRedo,
      handleUndo,
      handleRedo,
      handleAddText,
      handleToggleStickers,
      handleBackgroundSettings,
      handleDuplicate,
      handleDelete,
      handleMoveToFront,
      handleMoveToBack,
      handleClear,
      handleSaveTemplate,
      handleExport,
    }
  },
}
</script>

<style lang="scss" scoped>
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background-color: var(--tg-theme-secondary-bg-color, #ffffff);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  gap: 12px;
  overflow-x: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.06));

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    padding: 8px 16px;
    gap: 8px;
  }

  &__group {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
    padding: 4px;
    border-radius: 12px;

    @media (max-width: 768px) {
      gap: 4px;
    }
  }

  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: 10px;
    border: none;
    border-radius: 10px;
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    position: relative;

    @media (max-width: 768px) {
      min-width: 40px;
      min-height: 40px;
      padding: 8px;
    }

    &:hover:not(:disabled) {
      background-color: rgba(0, 0, 0, 0.08);
      transform: translateY(-1px) scale(1.02);
    }

    &:active:not(:disabled) {
      transform: translateY(0) scale(0.98);
    }

    &--active {
      background-color: var(--tg-theme-button-color, #007aff);
      color: var(--tg-theme-button-text-color, #ffffff);
      box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
      transform: translateY(-1px);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
        transform: translateY(-2px) scale(1.02);
      }
    }

    &--primary {
      background-color: var(--tg-theme-button-color, #007aff);
      color: var(--tg-theme-button-text-color, #ffffff);
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);

      &:hover {
        box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
        transform: translateY(-2px) scale(1.05);
      }
    }

    &--disabled,
    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
        transform: none;
      }
    }
  }
}

// Enhanced focus styles
button:focus-visible {
  outline: 2px solid var(--tg-theme-link-color, #007aff);
  outline-offset: 2px;
}

// Smooth animations for all interactive elements
* {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

{"name": "test", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@telegram-apps/sdk": "^3.11.4", "@vueuse/core": "^13.5.0", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "sass": "^1.89.2", "vue": "^3.5.17", "vue-i18n": "^11.1.9", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/test-utils": "^2.4.6", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "jsdom": "^26.1.0", "prettier": "3.5.3", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4"}}
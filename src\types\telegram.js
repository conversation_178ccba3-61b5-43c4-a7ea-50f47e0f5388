// Telegram WebApp types and interfaces (JSDoc format)

/**
 * @typedef {Object} TelegramUser
 * @property {number} id - Unique identifier for this user
 * @property {boolean} is_bot - True, if this user is a bot
 * @property {string} first_name - User's first name
 * @property {string} [last_name] - User's last name
 * @property {string} [username] - User's username
 * @property {string} [language_code] - IETF language tag of the user's language
 * @property {boolean} [is_premium] - True, if this user is a Telegram Premium user
 * @property {string} [photo_url] - URL of the user's profile photo
 */

/**
 * @typedef {Object} TelegramThemeParams
 * @property {string} [bg_color] - Background color in the #RRGGBB format
 * @property {string} [text_color] - Main text color in the #RRGGBB format
 * @property {string} [hint_color] - Hint text color in the #RRGGBB format
 * @property {string} [link_color] - Link color in the #RRGGBB format
 * @property {string} [button_color] - Button color in the #RRGGBB format
 * @property {string} [button_text_color] - Button text color in the #RRGGBB format
 * @property {string} [secondary_bg_color] - Secondary background color in the #RRGGBB format
 */

/**
 * @typedef {Object} TelegramInitData
 * @property {string} query_id - A unique identifier for the Web App session
 * @property {TelegramUser} [user] - An object containing data about the current user
 * @property {TelegramUser} [receiver] - An object containing data about the chat partner
 * @property {string} [chat] - An object containing data about the chat
 * @property {string} [chat_type] - Type of the chat
 * @property {string} [chat_instance] - Global identifier
 * @property {string} [start_param] - The value of the startattach parameter
 * @property {number} [can_send_after] - Unix time when a message can be sent via the answerWebAppQuery method
 * @property {number} auth_date - Unix time when the form was opened
 * @property {string} hash - A hash of all passed parameters
 */

/**
 * @typedef {Object} PopupParams
 * @property {string} title - The text to be displayed in the popup title, 0-64 characters
 * @property {string} message - The message to be displayed in the body of the popup, 1-256 characters
 * @property {PopupButton[]} [buttons] - List of buttons to be shown in the popup, 1-3 buttons
 */

/**
 * @typedef {Object} PopupButton
 * @property {string} [id] - Identifier of the button, 0-64 characters
 * @property {'default'|'ok'|'close'|'cancel'|'destructive'} [type] - Type of the button
 * @property {string} [text] - The text to be displayed on the button, 0-64 characters
 */

/**
 * @typedef {Object} MainButtonParams
 * @property {string} [text] - Button text
 * @property {string} [color] - Button color in the #RRGGBB format
 * @property {string} [text_color] - Button text color in the #RRGGBB format
 * @property {boolean} [is_active] - True if the button is active
 * @property {boolean} [is_visible] - True if the button is visible
 */

/**
 * @typedef {'light'|'medium'|'heavy'} HapticFeedbackType
 */

/**
 * @typedef {'error'|'success'|'warning'} NotificationType
 */

export {}

<template>
  <div class="canvas-guides" v-if="showGuides && selectedElement">
    <!-- Horizontal guides -->
    <div
      v-for="guide in horizontalGuides"
      :key="`h-${guide.position}`"
      class="guide guide--horizontal"
      :style="{ top: guide.position + 'px' }"
    >
      <div class="guide__line"></div>
      <div class="guide__label">{{ Math.round(guide.position) }}px</div>
    </div>

    <!-- Vertical guides -->
    <div
      v-for="guide in verticalGuides"
      :key="`v-${guide.position}`"
      class="guide guide--vertical"
      :style="{ left: guide.position + 'px' }"
    >
      <div class="guide__line"></div>
      <div class="guide__label">{{ Math.round(guide.position) }}px</div>
    </div>

    <!-- Snap indicators -->
    <div
      v-for="snap in snapIndicators"
      :key="`snap-${snap.x}-${snap.y}`"
      class="snap-indicator"
      :style="{
        left: snap.x + 'px',
        top: snap.y + 'px',
        transform: `translate(-50%, -50%)`,
      }"
    ></div>

    <!-- Distance indicators -->
    <div
      v-for="distance in distanceIndicators"
      :key="`dist-${distance.id}`"
      class="distance-indicator"
      :style="{
        left: distance.x + 'px',
        top: distance.y + 'px',
        width: distance.width + 'px',
        height: distance.height + 'px',
      }"
    >
      <div class="distance-indicator__line" :class="distance.direction"></div>
      <div class="distance-indicator__label">{{ Math.round(distance.value) }}px</div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useCanvasStore } from '@/stores/canvas'

export default {
  name: 'CanvasGuides',
  props: {
    showGuides: {
      type: Boolean,
      default: true,
    },
    snapThreshold: {
      type: Number,
      default: 10,
    },
  },
  setup(props) {
    const canvasStore = useCanvasStore()

    const selectedElement = computed(() => canvasStore.selectedElement)
    const canvasWidth = computed(() => canvasStore.width)
    const canvasHeight = computed(() => canvasStore.height)
    const elements = computed(() => canvasStore.elements)

    // Calculate horizontal guides (simplified for better performance)
    const horizontalGuides = computed(() => {
      if (!selectedElement.value) return []

      const guides = []
      const element = selectedElement.value

      // Canvas center line
      guides.push({
        position: canvasHeight.value / 2,
        type: 'canvas-center',
        label: 'Центр',
      })

      // Element center line
      guides.push({
        position: element.y + element.height / 2,
        type: 'element-center',
        label: 'Центр элемента',
      })

      return guides
    })

    // Calculate vertical guides (simplified for better performance)
    const verticalGuides = computed(() => {
      if (!selectedElement.value) return []

      const guides = []
      const element = selectedElement.value

      // Canvas center line
      guides.push({
        position: canvasWidth.value / 2,
        type: 'canvas-center',
        label: 'Центр',
      })

      // Element center line
      guides.push({
        position: element.x + element.width / 2,
        type: 'element-center',
        label: 'Центр элемента',
      })

      return guides
    })

    // Calculate snap indicators
    const snapIndicators = computed(() => {
      if (!selectedElement.value) return []

      const indicators = []
      const element = selectedElement.value

      // Check for snap points
      horizontalGuides.value.forEach((hGuide) => {
        verticalGuides.value.forEach((vGuide) => {
          indicators.push({
            x: vGuide.position,
            y: hGuide.position,
          })
        })
      })

      return indicators
    })

    // Calculate distance indicators
    const distanceIndicators = computed(() => {
      if (!selectedElement.value) return []

      const indicators = []
      const element = selectedElement.value

      // Distance to canvas edges
      if (element.x > 0) {
        indicators.push({
          id: 'left-edge',
          x: 0,
          y: element.y + element.height / 2,
          width: element.x,
          height: 1,
          direction: 'horizontal',
          value: element.x,
        })
      }

      if (element.x + element.width < canvasWidth.value) {
        indicators.push({
          id: 'right-edge',
          x: element.x + element.width,
          y: element.y + element.height / 2,
          width: canvasWidth.value - (element.x + element.width),
          height: 1,
          direction: 'horizontal',
          value: canvasWidth.value - (element.x + element.width),
        })
      }

      if (element.y > 0) {
        indicators.push({
          id: 'top-edge',
          x: element.x + element.width / 2,
          y: 0,
          width: 1,
          height: element.y,
          direction: 'vertical',
          value: element.y,
        })
      }

      if (element.y + element.height < canvasHeight.value) {
        indicators.push({
          id: 'bottom-edge',
          x: element.x + element.width / 2,
          y: element.y + element.height,
          width: 1,
          height: canvasHeight.value - (element.y + element.height),
          direction: 'vertical',
          value: canvasHeight.value - (element.y + element.height),
        })
      }

      return indicators
    })

    return {
      selectedElement,
      horizontalGuides,
      verticalGuides,
      snapIndicators,
      distanceIndicators,
    }
  },
}
</script>

<style lang="scss" scoped>
.canvas-guides {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.guide {
  position: absolute;
  pointer-events: none;

  &--horizontal {
    left: 0;
    right: 0;
    height: 1px;

    .guide__line {
      width: 100%;
      height: 1px;
      background: #007aff;
      opacity: 0.8;
    }

    .guide__label {
      position: absolute;
      left: 8px;
      top: -20px;
      background: #007aff;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      white-space: nowrap;
    }
  }

  &--vertical {
    top: 0;
    bottom: 0;
    width: 1px;

    .guide__line {
      width: 1px;
      height: 100%;
      background: #007aff;
      opacity: 0.8;
    }

    .guide__label {
      position: absolute;
      top: 8px;
      left: -30px;
      background: #007aff;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      white-space: nowrap;
      transform: rotate(-90deg);
      transform-origin: center;
    }
  }
}

.snap-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #007aff;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: snapPulse 0.3s ease-out;
}

@keyframes snapPulse {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

.distance-indicator {
  position: absolute;

  &__line {
    position: absolute;
    background: #ff6b35;

    &.horizontal {
      width: 100%;
      height: 1px;
      top: 50%;
      transform: translateY(-50%);

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border: 4px solid transparent;
        top: -4px;
      }

      &::before {
        left: -4px;
        border-right-color: #ff6b35;
      }

      &::after {
        right: -4px;
        border-left-color: #ff6b35;
      }
    }

    &.vertical {
      width: 1px;
      height: 100%;
      left: 50%;
      transform: translateX(-50%);

      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border: 4px solid transparent;
        left: -4px;
      }

      &::before {
        top: -4px;
        border-bottom-color: #ff6b35;
      }

      &::after {
        bottom: -4px;
        border-top-color: #ff6b35;
      }
    }
  }

  &__label {
    position: absolute;
    background: #ff6b35;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>

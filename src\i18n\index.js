import { createI18n } from 'vue-i18n'
import ru from './locales/ru.json'
import en from './locales/en.json'

// Get user's language from Telegram or browser
const getUserLanguage = () => {
  // Try to get language from Telegram WebApp
  if (window.Telegram?.WebApp?.initDataUnsafe?.user?.language_code) {
    const tgLang = window.Telegram.WebApp.initDataUnsafe.user.language_code
    // Map common language codes
    if (tgLang.startsWith('ru')) return 'ru'
    if (tgLang.startsWith('en')) return 'en'
  }
  
  // Fallback to browser language
  const browserLang = navigator.language || navigator.userLanguage
  if (browserLang.startsWith('ru')) return 'ru'
  
  // Default to English
  return 'en'
}

// Create i18n instance
const i18n = createI18n({
  legacy: false,
  locale: getUserLanguage(),
  fallbackLocale: 'en',
  messages: {
    ru,
    en
  }
})

export default i18n

// Helper function to change language
export const setLanguage = (locale) => {
  i18n.global.locale.value = locale
  localStorage.setItem('post-generator-language', locale)
}

// Helper function to get current language
export const getCurrentLanguage = () => {
  return i18n.global.locale.value
}

// Helper function to get available languages
export const getAvailableLanguages = () => {
  return [
    { code: 'ru', name: 'Русский', flag: '🇷🇺' },
    { code: 'en', name: 'English', flag: '🇺🇸' }
  ]
}

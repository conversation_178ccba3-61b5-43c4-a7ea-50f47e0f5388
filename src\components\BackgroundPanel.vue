<template>
  <div class="background-panel">
    <div class="bg-sections">
      <!-- Solid Colors -->
      <div class="bg-section">
        <h4>Цвета</h4>
        <div class="color-grid">
          <button
            v-for="color in colors"
            :key="color"
            class="color-option"
            :class="{ active: currentBackground === color }"
            :style="{ backgroundColor: color }"
            @click="setBackground(color)"
          ></button>
        </div>
      </div>

      <!-- Gradients -->
      <div class="bg-section">
        <h4>Градиенты</h4>
        <div class="gradient-grid">
          <button
            v-for="gradient in gradients"
            :key="gradient.id"
            class="gradient-option"
            :class="{ active: currentBackground === gradient.value }"
            :style="{ background: gradient.value }"
            @click="setBackground(gradient.value)"
          ></button>
        </div>
      </div>

      <!-- Transparent -->
      <div class="bg-section">
        <h4>Прозрачность</h4>
        <button
          class="transparent-option"
          :class="{ active: currentBackground === 'transparent' }"
          @click="setBackground('transparent')"
        >
          <div class="transparent-pattern"></div>
          <span>Прозрачный</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'

export default {
  name: 'BackgroundPanel',
  emits: ['close'],
  setup() {
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()

    const currentBackground = computed(() => canvasStore.background)

    const colors = [
      '#ffffff',
      '#f8f9fa',
      '#e9ecef',
      '#dee2e6',
      '#ced4da',
      '#000000',
      '#212529',
      '#343a40',
      '#495057',
      '#6c757d',
      '#007bff',
      '#6610f2',
      '#6f42c1',
      '#e83e8c',
      '#dc3545',
      '#fd7e14',
      '#ffc107',
      '#28a745',
      '#20c997',
      '#17a2b8',
      '#667eea',
      '#764ba2',
      '#f093fb',
      '#f5576c',
      '#4facfe',
      '#00f2fe',
      '#43e97b',
      '#38f9d7',
      '#ffecd2',
      '#fcb69f',
    ]

    const gradients = [
      { id: 'sunset', value: 'linear-gradient(135deg, #ff6b6b, #feca57)' },
      { id: 'ocean', value: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { id: 'forest', value: 'linear-gradient(135deg, #11998e, #38ef7d)' },
      { id: 'purple', value: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { id: 'pink', value: 'linear-gradient(135deg, #f093fb, #f5576c)' },
      { id: 'blue', value: 'linear-gradient(135deg, #4facfe, #00f2fe)' },
      { id: 'green', value: 'linear-gradient(135deg, #43e97b, #38f9d7)' },
      { id: 'orange', value: 'linear-gradient(135deg, #ffecd2, #fcb69f)' },
    ]

    const setBackground = (background) => {
      canvasStore.setBackground(background)
      hapticFeedback.selectionChanged()
    }

    return {
      currentBackground,
      colors,
      gradients,
      setBackground,
    }
  },
}
</script>

<style lang="scss" scoped>
.background-panel {
  padding: 20px;
  max-height: 50vh;
  overflow-y: auto;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.2));
    border-radius: 3px;
  }
}

.bg-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.bg-section h4 {
  margin: 0 0 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--tg-theme-text-color, #374151);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
}

.color-option {
  width: 40px;
  height: 40px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: scale(1.1);
  }

  &.active {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
  }

  &::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}

.gradient-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.gradient-option {
  width: 80px;
  height: 40px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &.active {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
  }
}

.transparent-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: 150px;

  &:hover {
    border-color: #667eea;
    transform: translateY(-2px);
  }

  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }

  .transparent-pattern {
    width: 32px;
    height: 32px;
    background-image:
      linear-gradient(45deg, #f3f4f6 25%, transparent 25%),
      linear-gradient(-45deg, #f3f4f6 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f3f4f6 75%),
      linear-gradient(-45deg, transparent 75%, #f3f4f6 75%);
    background-size: 8px 8px;
    background-position:
      0 0,
      0 4px,
      4px -4px,
      -4px 0px;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
  }

  span {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
  }
}
</style>

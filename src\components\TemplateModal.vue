<template>
  <div class="template-modal" @click="handleBackdropClick">
    <div class="template-modal__content" @click.stop>
      <div class="template-modal__header">
        <h2>
          <component :is="mode === 'save' ? 'Save' : 'FolderOpen'" :size="24" />
          {{ modalTitle }}
        </h2>
        <button class="template-modal__close" @click="$emit('close')">
          <X :size="20" />
        </button>
      </div>

      <div class="modal__body">
        <!-- Save template mode -->
        <div v-if="mode === 'save'" class="template-save">
          <div class="template-save__preview">
            <canvas ref="previewCanvas" width="200" height="150"></canvas>
          </div>

          <div class="template-save__form">
            <div class="form-field">
              <label>Название шаблона</label>
              <input
                v-model="templateName"
                type="text"
                placeholder="Введите название..."
                maxlength="50"
                @keyup.enter="handleSave"
              />
            </div>

            <div class="form-field">
              <label>Описание (необязательно)</label>
              <textarea
                v-model="templateDescription"
                placeholder="Краткое описание шаблона..."
                rows="3"
                maxlength="200"
              ></textarea>
            </div>

            <div class="form-field">
              <label>Теги (через запятую)</label>
              <input
                v-model="templateTags"
                type="text"
                placeholder="цитата, мотивация, бизнес..."
                @keyup.enter="handleSave"
              />
            </div>
          </div>

          <div class="template-save__actions">
            <button class="btn btn--secondary" @click="$emit('close')">Отмена</button>
            <button class="btn btn--primary" @click="handleSave" :disabled="!templateName.trim()">
              Сохранить
            </button>
          </div>
        </div>

        <!-- Load template mode -->
        <div v-else-if="mode === 'load'" class="template-load">
          <!-- Search and filters -->
          <div class="template-load__filters">
            <div class="form-field">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Поиск шаблонов..."
                class="search-input"
              />
            </div>

            <div class="template-tabs">
              <button
                :class="['template-tab', { active: activeTab === 'all' }]"
                @click="setActiveTab('all')"
              >
                Все ({{ filteredTemplates.length }})
              </button>
              <button
                :class="['template-tab', { active: activeTab === 'user' }]"
                @click="setActiveTab('user')"
              >
                Мои ({{ userTemplates.length }})
              </button>
              <button
                :class="['template-tab', { active: activeTab === 'default' }]"
                @click="setActiveTab('default')"
              >
                Стандартные ({{ defaultTemplates.length }})
              </button>
            </div>
          </div>

          <!-- Templates grid -->
          <div class="template-grid" v-if="displayedTemplates.length > 0">
            <div
              v-for="template in displayedTemplates"
              :key="template.id"
              class="template-card"
              @click="handleLoad(template)"
            >
              <div class="template-card__thumbnail">
                <img v-if="template.thumbnail" :src="template.thumbnail" :alt="template.name" />
                <div v-else class="template-card__placeholder">📄</div>
              </div>

              <div class="template-card__info">
                <h4>{{ template.name }}</h4>
                <p v-if="template.description">{{ template.description }}</p>
                <div class="template-card__meta">
                  <span class="template-card__date">
                    {{ formatDate(template.updatedAt) }}
                  </span>
                  <div class="template-card__tags">
                    <span v-for="tag in template.tags.slice(0, 3)" :key="tag" class="tag">
                      {{ tag }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="template-card__actions" v-if="!template.isDefault">
                <button
                  class="template-card__action"
                  @click.stop="handleDelete(template)"
                  title="Удалить"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>

          <!-- Empty state -->
          <div v-else class="template-empty">
            <div class="template-empty__icon">📄</div>
            <h3>Шаблоны не найдены</h3>
            <p>{{ emptyStateMessage }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTemplates } from '@/composables/useTemplates'
import { useTelegram } from '@/composables/useTelegram'
import {
  Save,
  FolderOpen,
  X,
  Play,
  Trash2,
  FileX,
  Loader2,
  Hash,
  Briefcase,
  User,
  GraduationCap,
  MoreHorizontal,
  Grid,
} from 'lucide-vue-next'

export default {
  name: 'TemplateModal',
  components: {
    Save,
    FolderOpen,
    X,
    Play,
    Trash2,
    FileX,
    Loader2,
    Hash,
    Briefcase,
    User,
    GraduationCap,
    MoreHorizontal,
    Grid,
  },
  props: {
    mode: {
      type: String,
      required: true,
      validator: (value) => ['save', 'load'].includes(value),
    },
  },
  emits: ['close', 'template-saved', 'template-loaded'],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { templates, userTemplates, defaultTemplates, createTemplate, deleteTemplate } =
      useTemplates()
    const { hapticFeedback, showConfirm } = useTelegram()

    // Refs
    const previewCanvas = ref(null)

    // Save mode state
    const templateName = ref('')
    const templateDescription = ref('')
    const templateTags = ref('')

    // Load mode state
    const searchQuery = ref('')
    const activeTab = ref('all')

    // Computed
    const modalTitle = computed(() => {
      return props.mode === 'save' ? 'Сохранить шаблон' : 'Загрузить шаблон'
    })

    const filteredTemplates = computed(() => {
      if (!searchQuery.value.trim()) {
        return templates.value
      }

      const query = searchQuery.value.toLowerCase()
      return templates.value.filter(
        (template) =>
          template.name.toLowerCase().includes(query) ||
          template.description.toLowerCase().includes(query) ||
          template.tags.some((tag) => tag.includes(query)),
      )
    })

    const displayedTemplates = computed(() => {
      const filtered = filteredTemplates.value

      switch (activeTab.value) {
        case 'user':
          return filtered.filter((t) => !t.isDefault)
        case 'default':
          return filtered.filter((t) => t.isDefault)
        default:
          return filtered
      }
    })

    const emptyStateMessage = computed(() => {
      if (searchQuery.value.trim()) {
        return 'Попробуйте изменить поисковый запрос'
      }

      switch (activeTab.value) {
        case 'user':
          return 'У вас пока нет сохраненных шаблонов'
        case 'default':
          return 'Стандартные шаблоны недоступны'
        default:
          return 'Шаблоны не найдены'
      }
    })

    // Methods
    const handleBackdropClick = () => {
      emit('close')
    }

    const setActiveTab = (tab) => {
      activeTab.value = tab
      hapticFeedback.selectionChanged()
    }

    const generatePreview = async () => {
      if (!previewCanvas.value) return

      const canvas = previewCanvas.value
      const ctx = canvas.getContext('2d')

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Render current canvas state as preview
      // This is a simplified version - in real implementation,
      // you'd render the actual canvas content
      const bg = canvasStore.background

      if (bg.type === 'color') {
        ctx.fillStyle = bg.value
        ctx.fillRect(0, 0, canvas.width, canvas.height)
      } else if (bg.type === 'gradient') {
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height)
        bg.colors.forEach((color, index) => {
          gradient.addColorStop(index / (bg.colors.length - 1), color)
        })
        ctx.fillStyle = gradient
        ctx.fillRect(0, 0, canvas.width, canvas.height)
      }

      // Render elements (simplified)
      canvasStore.elements.forEach((element) => {
        if (element.type === 'text') {
          ctx.fillStyle = element.color
          ctx.font = `${Math.min(element.fontSize * 0.2, 16)}px ${element.fontFamily}`
          ctx.textAlign = element.textAlign || 'left'
          ctx.fillText(element.text, element.x * 0.25, element.y * 0.25 + 16)
        }
      })
    }

    const handleSave = async () => {
      if (!templateName.value.trim()) return

      try {
        // Generate thumbnail
        let thumbnail = ''
        if (previewCanvas.value) {
          thumbnail = previewCanvas.value.toDataURL('image/jpeg', 0.8)
        }

        // Parse tags
        const tags = templateTags.value
          .split(',')
          .map((tag) => tag.trim())
          .filter((tag) => tag.length > 0)

        // Create template
        const template = createTemplate(
          templateName.value,
          templateDescription.value,
          canvasStore.exportCanvas(),
          thumbnail,
          tags,
        )

        emit('template-saved', template)
        hapticFeedback.notificationOccurred('success')
      } catch (error) {
        console.error('Failed to save template:', error)
        hapticFeedback.notificationOccurred('error')
      }
    }

    const handleLoad = (template) => {
      canvasStore.importCanvas(template.data)
      emit('template-loaded', template)
      hapticFeedback.impactOccurred('medium')
    }

    const handleDelete = async (template) => {
      const confirmed = await showConfirm(
        `Удалить шаблон "${template.name}"? Это действие нельзя отменить.`,
      )

      if (confirmed) {
        try {
          deleteTemplate(template.id)
          hapticFeedback.notificationOccurred('success')
        } catch (error) {
          console.error('Failed to delete template:', error)
          hapticFeedback.notificationOccurred('error')
        }
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      })
    }

    // Initialize
    onMounted(async () => {
      if (props.mode === 'save') {
        await nextTick()
        generatePreview()
      }
    })

    return {
      previewCanvas,
      templateName,
      templateDescription,
      templateTags,
      searchQuery,
      activeTab,
      modalTitle,
      filteredTemplates,
      displayedTemplates,
      userTemplates,
      defaultTemplates,
      emptyStateMessage,
      handleBackdropClick,
      setActiveTab,
      handleSave,
      handleLoad,
      handleDelete,
      formatDate,
    }
  },
}
</script>

<style lang="scss" scoped>
.template-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  padding: 20px;
  backdrop-filter: blur(4px);

  &__content {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 24px;

    h2 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      font-size: 24px;
      font-weight: 700;
      color: #1a1a1a;
    }
  }

  &__close {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 12px;
    background: transparent;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }

    &:hover {
      background-color: var(--tg-theme-button-color, #007aff);
      color: var(--tg-theme-button-text-color, #ffffff);
    }
  }

  &__body {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }
}

// Save template styles
.template-save {
  &__preview {
    text-align: center;
    margin-bottom: 24px;

    canvas {
      border: 1px solid var(--tg-theme-hint-color, #e5e5e7);
      border-radius: 8px;
      background-color: #f8f9fa;
    }
  }

  &__form {
    margin-bottom: 24px;
  }

  &__actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}

// Load template styles
.template-load {
  &__filters {
    margin-bottom: 24px;
  }
}

.template-tabs {
  display: flex;
  gap: 4px;
  margin-top: 12px;
}

.template-tab {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--tg-theme-hint-color, #e5e5e7);
  border-radius: 6px;
  background-color: transparent;
  color: var(--tg-theme-text-color, #000000);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  &.active {
    background-color: var(--tg-theme-button-color, #007aff);
    color: var(--tg-theme-button-text-color, #ffffff);
    border-color: var(--tg-theme-button-color, #007aff);
  }
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.template-card {
  border: 1px solid var(--tg-theme-hint-color, #e5e5e7);
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: var(--tg-theme-link-color, #007aff);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &__thumbnail {
    height: 120px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__placeholder {
    font-size: 32px;
    color: var(--tg-theme-hint-color, #8e8e93);
  }

  &__info {
    padding: 12px;

    h4 {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--tg-theme-text-color, #000000);
    }

    p {
      margin: 0 0 8px 0;
      font-size: 12px;
      color: var(--tg-theme-hint-color, #8e8e93);
      line-height: 1.3;
    }
  }

  &__meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
  }

  &__date {
    font-size: 11px;
    color: var(--tg-theme-hint-color, #8e8e93);
  }

  &__tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }

  &__actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
  }

  &__action {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;

    &:hover {
      background-color: #ff3b30;
      color: white;
    }
  }
}

.template-empty {
  text-align: center;
  padding: 48px 16px;

  &__icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    color: var(--tg-theme-text-color, #000000);
  }

  p {
    margin: 0;
    color: var(--tg-theme-hint-color, #8e8e93);
    font-size: 14px;
  }
}

.form-field {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--tg-theme-text-color, #000000);
    font-size: 14px;
  }

  input,
  textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--tg-theme-hint-color, #c6c6c8);
    border-radius: 6px;
    background-color: var(--tg-theme-bg-color, #ffffff);
    color: var(--tg-theme-text-color, #000000);
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: var(--tg-theme-link-color, #007aff);
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
    }

    &::placeholder {
      color: var(--tg-theme-hint-color, #8e8e93);
    }
  }

  textarea {
    resize: vertical;
    min-height: 60px;
  }
}

.search-input {
  font-size: 16px !important;
  padding: 12px 16px !important;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &--primary {
    background-color: var(--tg-theme-button-color, #007aff);
    color: var(--tg-theme-button-text-color, #ffffff);

    &:hover:not(:disabled) {
      opacity: 0.9;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  &--secondary {
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    border: 1px solid var(--tg-theme-hint-color, #c6c6c8);

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}

.tag {
  font-size: 10px;
  padding: 2px 6px;
  background-color: var(--tg-theme-button-color, #007aff);
  color: var(--tg-theme-button-text-color, #ffffff);
  border-radius: 10px;
  white-space: nowrap;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@media (max-width: 768px) {
  .template-modal {
    padding: 12px;

    &__content {
      border-radius: 16px;
      max-height: 95vh;
    }

    &__header {
      padding: 20px 20px 0;

      h2 {
        font-size: 20px;
      }
    }
  }
}
</style>

<template>
  <div class="text-editor" v-if="selectedElement && selectedElement.type === 'text'">
    <div class="text-editor__header" @click="toggleCollapse">
      <div class="text-editor__header-content">
        <Type class="text-editor__icon" :size="20" />
        <h3>Редактирование текста</h3>
      </div>
      <div class="text-editor__header-actions">
        <button
          class="text-editor__toggle"
          @click.stop="toggleCollapse"
          :class="{ collapsed: isCollapsed }"
        >
          <ChevronDown :size="20" />
        </button>
        <button class="text-editor__close" @click="$emit('close')">
          <X :size="18" />
        </button>
      </div>
    </div>

    <TransitionGroup name="collapse">
      <div class="text-editor__content" v-show="!isCollapsed">
        <!-- Text content -->
        <div class="text-editor__field">
          <label>Текст</label>
          <textarea
            v-model="localText"
            @input="updateText"
            placeholder="Введите текст..."
            rows="3"
          />
        </div>
      </div>

      <!-- Font settings -->
      <div class="text-editor__row">
        <div class="text-editor__field">
          <label>Шрифт</label>
          <select v-model="localFontFamily" @change="updateFontFamily">
            <option value="Arial, sans-serif">Arial</option>
            <option value="Helvetica, sans-serif">Helvetica</option>
            <option value="Georgia, serif">Georgia</option>
            <option value="Times New Roman, serif">Times</option>
            <option value="Courier New, monospace">Courier</option>
            <option value="Impact, sans-serif">Impact</option>
            <option value="Comic Sans MS, cursive">Comic Sans</option>
          </select>
        </div>

        <div class="text-editor__field">
          <label>Размер</label>
          <input
            type="range"
            v-model="localFontSize"
            @input="updateFontSize"
            min="12"
            max="120"
            step="2"
          />
          <span class="text-editor__value">{{ localFontSize }}px</span>
        </div>
      </div>

      <!-- Text style -->
      <div class="text-editor__row">
        <div class="text-editor__field">
          <label>Выравнивание</label>
          <div class="text-editor__button-group">
            <button :class="{ active: localTextAlign === 'left' }" @click="updateTextAlign('left')">
              <AlignLeft :size="16" />
            </button>
            <button
              :class="{ active: localTextAlign === 'center' }"
              @click="updateTextAlign('center')"
            >
              <AlignCenter :size="16" />
            </button>
            <button
              :class="{ active: localTextAlign === 'right' }"
              @click="updateTextAlign('right')"
            >
              <AlignRight :size="16" />
            </button>
          </div>
        </div>

        <div class="text-editor__field">
          <label>Стиль</label>
          <div class="text-editor__button-group">
            <button :class="{ active: localFontWeight === 'bold' }" @click="toggleBold">
              <Bold :size="16" />
            </button>
            <button :class="{ active: localFontStyle === 'italic' }" @click="toggleItalic">
              <Italic :size="16" />
            </button>
          </div>
        </div>
      </div>

      <!-- Colors -->
      <div class="text-editor__row">
        <div class="text-editor__field">
          <label>
            <Palette class="text-editor__label-icon" :size="14" />
            Цвет текста
          </label>
          <div class="text-editor__color-row">
            <input
              type="color"
              v-model="localColor"
              @input="updateColor"
              class="text-editor__color-picker"
            />
            <input
              type="text"
              v-model="localColor"
              @input="updateColor"
              placeholder="#000000"
              class="text-editor__color-input"
            />
          </div>
        </div>
      </div>

      <!-- Stroke -->
      <div class="text-editor__section">
        <h4>
          <Square class="text-editor__section-icon" :size="16" />
          Обводка
        </h4>
        <div class="text-editor__row">
          <div class="text-editor__field">
            <label>Цвет обводки</label>
            <div class="text-editor__color-row">
              <input
                type="color"
                v-model="localStrokeColor"
                @input="updateStrokeColor"
                class="text-editor__color-picker"
              />
              <input
                type="text"
                v-model="localStrokeColor"
                @input="updateStrokeColor"
                placeholder="#000000"
                class="text-editor__color-input"
              />
            </div>
          </div>

          <div class="text-editor__field">
            <label>Толщина</label>
            <input
              type="range"
              v-model="localStrokeWidth"
              @input="updateStrokeWidth"
              min="0"
              max="10"
              step="1"
            />
            <span class="text-editor__value">{{ localStrokeWidth }}px</span>
          </div>
        </div>
      </div>

      <!-- Shadow -->
      <div class="text-editor__section">
        <h4>
          <Layers class="text-editor__section-icon" :size="16" />
          Тень
        </h4>
        <div class="text-editor__row">
          <div class="text-editor__field">
            <label>Цвет тени</label>
            <div class="text-editor__color-row">
              <input
                type="color"
                v-model="localShadowColor"
                @input="updateShadowColor"
                class="text-editor__color-picker"
              />
              <input
                type="text"
                v-model="localShadowColor"
                @input="updateShadowColor"
                placeholder="#000000"
                class="text-editor__color-input"
              />
            </div>
          </div>
        </div>

        <div class="text-editor__row">
          <div class="text-editor__field">
            <label>Размытие</label>
            <input
              type="range"
              v-model="localShadowBlur"
              @input="updateShadowBlur"
              min="0"
              max="20"
              step="1"
            />
            <span class="text-editor__value">{{ localShadowBlur }}px</span>
          </div>
        </div>

        <div class="text-editor__row">
          <div class="text-editor__field">
            <label>Смещение X</label>
            <input
              type="range"
              v-model="localShadowOffsetX"
              @input="updateShadowOffsetX"
              min="-20"
              max="20"
              step="1"
            />
            <span class="text-editor__value">{{ localShadowOffsetX }}px</span>
          </div>

          <div class="text-editor__field">
            <label>Смещение Y</label>
            <input
              type="range"
              v-model="localShadowOffsetY"
              @input="updateShadowOffsetY"
              min="-20"
              max="20"
              step="1"
            />
            <span class="text-editor__value">{{ localShadowOffsetY }}px</span>
          </div>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script>
import { ref, watch, computed } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import {
  Type,
  X,
  ChevronDown,
  Palette,
  Square,
  Layers,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
} from 'lucide-vue-next'

export default {
  name: 'TextEditor',
  components: {
    Type,
    X,
    ChevronDown,
    Palette,
    Square,
    Layers,
    AlignLeft,
    AlignCenter,
    AlignRight,
    Bold,
    Italic,
  },
  emits: ['close'],
  setup() {
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()

    const selectedElement = computed(() => canvasStore.selectedElement)

    // Collapse state
    const isCollapsed = ref(false)

    // Local reactive properties
    const localText = ref('')
    const localFontFamily = ref('Arial, sans-serif')
    const localFontSize = ref(24)
    const localTextAlign = ref('left')
    const localFontWeight = ref('normal')
    const localFontStyle = ref('normal')
    const localColor = ref('#000000')
    const localStrokeColor = ref('#000000')
    const localStrokeWidth = ref(0)
    const localShadowColor = ref('#000000')
    const localShadowBlur = ref(0)
    const localShadowOffsetX = ref(0)
    const localShadowOffsetY = ref(0)

    // Watch for selected element changes
    watch(
      selectedElement,
      (element) => {
        if (element && element.type === 'text') {
          localText.value = element.text || ''
          localFontFamily.value = element.fontFamily || 'Arial, sans-serif'
          localFontSize.value = element.fontSize || 24
          localTextAlign.value = element.textAlign || 'left'
          localFontWeight.value = element.fontWeight || 'normal'
          localFontStyle.value = element.fontStyle || 'normal'
          localColor.value = element.color || '#000000'
          localStrokeColor.value = element.strokeColor || '#000000'
          localStrokeWidth.value = element.strokeWidth || 0
          localShadowColor.value = element.shadowColor || '#000000'
          localShadowBlur.value = element.shadowBlur || 0
          localShadowOffsetX.value = element.shadowOffsetX || 0
          localShadowOffsetY.value = element.shadowOffsetY || 0
        }
      },
      { immediate: true },
    )

    // Update methods
    const updateElement = (updates) => {
      if (selectedElement.value) {
        canvasStore.updateElement(selectedElement.value.id, updates)
      }
    }

    const updateText = () => {
      updateElement({ text: localText.value })
      hapticFeedback.selectionChanged()
    }

    const updateFontFamily = () => {
      updateElement({ fontFamily: localFontFamily.value })
      hapticFeedback.selectionChanged()
    }

    const updateFontSize = () => {
      updateElement({ fontSize: parseInt(localFontSize.value) })
      hapticFeedback.selectionChanged()
    }

    const updateTextAlign = (align) => {
      localTextAlign.value = align
      updateElement({ textAlign: align })
      hapticFeedback.impactOccurred('light')
    }

    const toggleBold = () => {
      const newWeight = localFontWeight.value === 'bold' ? 'normal' : 'bold'
      localFontWeight.value = newWeight
      updateElement({ fontWeight: newWeight })
      hapticFeedback.impactOccurred('light')
    }

    const toggleItalic = () => {
      const newStyle = localFontStyle.value === 'italic' ? 'normal' : 'italic'
      localFontStyle.value = newStyle
      updateElement({ fontStyle: newStyle })
      hapticFeedback.impactOccurred('light')
    }

    const updateColor = () => {
      updateElement({ color: localColor.value })
      hapticFeedback.selectionChanged()
    }

    const updateStrokeColor = () => {
      updateElement({ strokeColor: localStrokeColor.value })
      hapticFeedback.selectionChanged()
    }

    const updateStrokeWidth = () => {
      updateElement({ strokeWidth: parseInt(localStrokeWidth.value) })
      hapticFeedback.selectionChanged()
    }

    const updateShadowColor = () => {
      updateElement({ shadowColor: localShadowColor.value })
      hapticFeedback.selectionChanged()
    }

    const updateShadowBlur = () => {
      updateElement({ shadowBlur: parseInt(localShadowBlur.value) })
      hapticFeedback.selectionChanged()
    }

    const updateShadowOffsetX = () => {
      updateElement({ shadowOffsetX: parseInt(localShadowOffsetX.value) })
      hapticFeedback.selectionChanged()
    }

    const updateShadowOffsetY = () => {
      updateElement({ shadowOffsetY: parseInt(localShadowOffsetY.value) })
      hapticFeedback.selectionChanged()
    }

    // Toggle collapse
    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
      hapticFeedback.impactOccurred('light')
    }

    return {
      selectedElement,
      isCollapsed,
      toggleCollapse,
      localText,
      localFontFamily,
      localFontSize,
      localTextAlign,
      localFontWeight,
      localFontStyle,
      localColor,
      localStrokeColor,
      localStrokeWidth,
      localShadowColor,
      localShadowBlur,
      localShadowOffsetX,
      localShadowOffsetY,
      updateText,
      updateFontFamily,
      updateFontSize,
      updateTextAlign,
      toggleBold,
      toggleItalic,
      updateColor,
      updateStrokeColor,
      updateStrokeWidth,
      updateShadowColor,
      updateShadowBlur,
      updateShadowOffsetX,
      updateShadowOffsetY,
    }
  },
}
</script>

<style lang="scss" scoped>
.text-editor {
  background-color: var(--tg-theme-secondary-bg-color, #ffffff);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.06));

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid var(--tg-theme-hint-color, #e5e5e7);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &-content {
      display: flex;
      align-items: center;
      gap: 12px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--tg-theme-text-color, #000000);
      }
    }

    &-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  &__icon {
    color: var(--tg-theme-link-color, #007aff);
    flex-shrink: 0;
  }

  &__section-icon {
    color: var(--tg-theme-link-color, #007aff);
    margin-right: 8px;
  }

  &__label-icon {
    color: var(--tg-theme-link-color, #007aff);
    margin-right: 6px;
    vertical-align: middle;
  }

  &__toggle {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 12px;
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
    }

    &.collapsed {
      transform: rotate(-90deg);
    }
  }

  &__close {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 12px;
    background-color: transparent;
    color: var(--tg-theme-text-color, #000000);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: #ff3b30;
      color: white;
      transform: scale(1.05);
    }
  }

  &__content {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.2));
      border-radius: 3px;

      &:hover {
        background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.3));
      }
    }
  }

  &__field {
    margin-bottom: 20px;

    label {
      display: block;
      margin-bottom: 10px;
      font-weight: 600;
      color: var(--tg-theme-text-color, #000000);
      font-size: 14px;
    }

    input,
    textarea,
    select {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
      border-radius: 12px;
      background-color: var(--tg-theme-bg-color, #ffffff);
      color: var(--tg-theme-text-color, #000000);
      font-size: 14px;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:focus {
        outline: none;
        border-color: var(--tg-theme-link-color, #007aff);
        box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        transform: translateY(-1px);
      }

      &:hover {
        border-color: var(--tg-theme-link-color, rgba(0, 122, 255, 0.3));
      }
    }

    input[type='range'] {
      padding: 0;
      background: transparent;
      border: none;
      height: 6px;
      border-radius: 3px;

      &:focus {
        box-shadow: none;
        transform: none;
      }

      &::-webkit-slider-thumb {
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--tg-theme-link-color, #007aff);
        cursor: pointer;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }
      }

      &::-webkit-slider-track {
        height: 6px;
        border-radius: 3px;
        background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
      }
    }
  }

  &__row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 8px;
    }
  }

  &__section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.06));

    h4 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--tg-theme-text-color, #000000);
      display: flex;
      align-items: center;
    }
  }

  &__button-group {
    display: flex;
    gap: 8px;
    background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
    padding: 4px;
    border-radius: 12px;

    button {
      flex: 1;
      padding: 12px 16px;
      border: none;
      border-radius: 8px;
      background-color: transparent;
      color: var(--tg-theme-text-color, #000000);
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 44px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
      }

      &.active {
        background-color: var(--tg-theme-button-color, #007aff);
        color: var(--tg-theme-button-text-color, #ffffff);
        box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
        transform: translateY(-1px);
      }
    }
  }

  &__color-row {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  &__color-picker {
    width: 40px !important;
    height: 40px;
    padding: 0 !important;
    border: 1px solid var(--tg-theme-hint-color, #c6c6c8);
    border-radius: 8px;
    cursor: pointer;

    &::-webkit-color-swatch-wrapper {
      padding: 0;
    }

    &::-webkit-color-swatch {
      border: none;
      border-radius: 6px;
    }
  }

  &__color-input {
    flex: 1;
    font-family: monospace;
  }

  &__value {
    font-size: 12px;
    color: var(--tg-theme-hint-color, #8e8e93);
    margin-left: 12px;
    min-width: 50px;
    text-align: right;
    font-weight: 600;
    background-color: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.04));
    padding: 4px 8px;
    border-radius: 6px;
  }
}

// Collapse animation
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.collapse-enter-to,
.collapse-leave-from {
  max-height: 1000px;
  opacity: 1;
  transform: translateY(0);
}

// Smooth animations for all interactive elements
* {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Enhanced focus styles
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid var(--tg-theme-link-color, #007aff);
  outline-offset: 2px;
}
</style>

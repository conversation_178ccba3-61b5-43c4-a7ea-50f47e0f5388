<template>
  <div class="unified-editor" v-if="selectedElement">
    <div class="editor-tabs">
      <button
        v-for="tab in availableTabs"
        :key="tab.id"
        class="tab-btn"
        :class="{ active: activeTab === tab.id }"
        @click="setActiveTab(tab.id)"
      >
        <component :is="tab.icon" :size="18" />
        <span>{{ tab.name }}</span>
      </button>
    </div>

    <div class="editor-content">
      <!-- Content Tab -->
      <div v-if="activeTab === 'content'" class="tab-content">
        <!-- Text Content -->
        <div v-if="selectedElement.type === 'text'" class="content-section">
          <label>Текст</label>
          <textarea
            v-model="elementData.text"
            @input="updateElement"
            placeholder="Введите текст..."
            rows="3"
            class="text-input"
          ></textarea>
        </div>

        <!-- Image Content -->
        <div v-if="selectedElement.type === 'image'" class="content-section">
          <label>Изображение</label>
          <div class="image-preview">
            <img :src="selectedElement.src" alt="Preview" />
            <button class="change-image-btn" @click="changeImage">
              <ImageIcon :size="16" />
              Изменить
            </button>
          </div>
        </div>

        <!-- Shape Content -->
        <div v-if="selectedElement.type === 'shape'" class="content-section">
          <label>Тип фигуры</label>
          <div class="shape-selector">
            <button
              v-for="shape in shapeTypes"
              :key="shape.type"
              class="shape-option"
              :class="{ active: elementData.shapeType === shape.type }"
              @click="setShapeType(shape.type)"
            >
              <component :is="shape.icon" :size="20" />
            </button>
          </div>
        </div>
      </div>

      <!-- Style Tab -->
      <div v-if="activeTab === 'style'" class="tab-content">
        <!-- Typography (for text) -->
        <div v-if="selectedElement.type === 'text'" class="style-section">
          <div class="style-group">
            <label>Шрифт</label>
            <select v-model="elementData.fontFamily" @change="updateElement" class="font-select">
              <option v-for="font in fonts" :key="font.value" :value="font.value">
                {{ font.name }}
              </option>
            </select>
          </div>

          <div class="style-group">
            <label>Размер: {{ elementData.fontSize }}px</label>
            <input
              type="range"
              v-model="elementData.fontSize"
              @input="updateElement"
              min="12"
              max="120"
              class="slider"
            />
          </div>

          <div class="style-group">
            <label>Начертание</label>
            <div class="style-buttons">
              <button
                class="style-btn"
                :class="{ active: elementData.fontWeight === 'bold' }"
                @click="toggleFontWeight"
              >
                <Bold :size="16" />
              </button>
              <button
                class="style-btn"
                :class="{ active: elementData.fontStyle === 'italic' }"
                @click="toggleFontStyle"
              >
                <Italic :size="16" />
              </button>
            </div>
          </div>

          <div class="style-group">
            <label>Выравнивание</label>
            <div class="align-buttons">
              <button
                v-for="align in alignments"
                :key="align.value"
                class="align-btn"
                :class="{ active: elementData.textAlign === align.value }"
                @click="setTextAlign(align.value)"
              >
                <component :is="align.icon" :size="16" />
              </button>
            </div>
          </div>
        </div>

        <!-- Color Controls -->
        <div class="style-section">
          <div class="style-group">
            <label>{{ selectedElement.type === 'text' ? 'Цвет текста' : 'Цвет заливки' }}</label>
            <div class="color-controls">
              <input
                type="color"
                v-model="elementData.color"
                @input="updateElement"
                class="color-picker"
              />
              <input
                type="text"
                v-model="elementData.color"
                @input="updateElement"
                class="color-input"
                placeholder="#000000"
              />
            </div>
          </div>

          <!-- Stroke for shapes -->
          <div v-if="selectedElement.type === 'shape'" class="style-group">
            <label>Обводка</label>
            <div class="stroke-controls">
              <input
                type="color"
                v-model="elementData.stroke"
                @input="updateElement"
                class="color-picker"
              />
              <input
                type="range"
                v-model="elementData.strokeWidth"
                @input="updateElement"
                min="0"
                max="20"
                class="slider"
              />
              <span>{{ elementData.strokeWidth }}px</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Transform Tab -->
      <div v-if="activeTab === 'transform'" class="tab-content">
        <div class="transform-section">
          <!-- Position -->
          <div class="transform-group">
            <label>Позиция</label>
            <div class="position-grid">
              <button
                v-for="(pos, index) in quickPositions"
                :key="index"
                class="position-btn"
                @click="setQuickPosition(pos)"
              >
                <div class="position-dot" :style="pos.style"></div>
              </button>
            </div>
          </div>

          <!-- Precise Position -->
          <div class="transform-group">
            <label>Точная позиция</label>
            <div class="input-row">
              <div class="input-group">
                <label>X</label>
                <input
                  type="number"
                  v-model="elementData.x"
                  @input="updateElement"
                  class="number-input"
                />
              </div>
              <div class="input-group">
                <label>Y</label>
                <input
                  type="number"
                  v-model="elementData.y"
                  @input="updateElement"
                  class="number-input"
                />
              </div>
            </div>
          </div>

          <!-- Size -->
          <div class="transform-group">
            <label>Размер</label>
            <div class="input-row">
              <div class="input-group">
                <label>Ширина</label>
                <input
                  type="number"
                  v-model="elementData.width"
                  @input="updateElement"
                  class="number-input"
                />
              </div>
              <div class="input-group">
                <label>Высота</label>
                <input
                  type="number"
                  v-model="elementData.height"
                  @input="updateElement"
                  class="number-input"
                />
              </div>
            </div>
          </div>

          <!-- Rotation -->
          <div class="transform-group">
            <label>Поворот: {{ elementData.rotation || 0 }}°</label>
            <input
              type="range"
              v-model="elementData.rotation"
              @input="updateElement"
              min="0"
              max="360"
              class="slider"
            />
          </div>

          <!-- Opacity -->
          <div class="transform-group">
            <label>Прозрачность: {{ Math.round((elementData.opacity || 1) * 100) }}%</label>
            <input
              type="range"
              v-model="elementData.opacity"
              @input="updateElement"
              min="0"
              max="1"
              step="0.01"
              class="slider"
            />
          </div>
        </div>
      </div>

      <!-- Effects Tab -->
      <div v-if="activeTab === 'effects'" class="tab-content">
        <div class="effects-section">
          <!-- Shadow -->
          <div class="effect-group">
            <label>Тень</label>
            <div class="shadow-controls">
              <div class="input-row">
                <div class="input-group">
                  <label>Цвет</label>
                  <input
                    type="color"
                    v-model="shadowColor"
                    @input="updateShadow"
                    class="color-picker"
                  />
                </div>
                <div class="input-group">
                  <label>Размытие</label>
                  <input
                    type="range"
                    v-model="shadowBlur"
                    @input="updateShadow"
                    min="0"
                    max="50"
                    class="slider"
                  />
                </div>
              </div>
              <div class="input-row">
                <div class="input-group">
                  <label>Смещение X</label>
                  <input
                    type="range"
                    v-model="shadowOffsetX"
                    @input="updateShadow"
                    min="-20"
                    max="20"
                    class="slider"
                  />
                </div>
                <div class="input-group">
                  <label>Смещение Y</label>
                  <input
                    type="range"
                    v-model="shadowOffsetY"
                    @input="updateShadow"
                    min="-20"
                    max="20"
                    class="slider"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Filters (for images) -->
          <div v-if="selectedElement.type === 'image'" class="effect-group">
            <label>Фильтры</label>
            <div class="filter-controls">
              <div class="filter-item">
                <label>Яркость: {{ filters.brightness }}%</label>
                <input
                  type="range"
                  v-model="filters.brightness"
                  @input="updateFilters"
                  min="0"
                  max="200"
                  class="slider"
                />
              </div>
              <div class="filter-item">
                <label>Контрастность: {{ filters.contrast }}%</label>
                <input
                  type="range"
                  v-model="filters.contrast"
                  @input="updateFilters"
                  min="0"
                  max="200"
                  class="slider"
                />
              </div>
              <div class="filter-item">
                <label>Насыщенность: {{ filters.saturation }}%</label>
                <input
                  type="range"
                  v-model="filters.saturation"
                  @input="updateFilters"
                  min="0"
                  max="200"
                  class="slider"
                />
              </div>
              <div class="filter-item">
                <label>Размытие: {{ filters.blur }}px</label>
                <input
                  type="range"
                  v-model="filters.blur"
                  @input="updateFilters"
                  min="0"
                  max="10"
                  class="slider"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import {
  Type,
  Palette,
  Move,
  Sparkles,
  Bold,
  Italic,
  AlignLeft,
  AlignCenter,
  AlignRight,
  ImageIcon,
  Square,
  Circle,
  Triangle,
  Star,
  Heart,
  Hexagon,
} from 'lucide-vue-next'

export default {
  name: 'UnifiedElementEditor',
  components: {
    Type,
    Palette,
    Move,
    Sparkles,
    Bold,
    Italic,
    AlignLeft,
    AlignCenter,
    AlignRight,
    ImageIcon,
    Square,
    Circle,
    Triangle,
    Star,
    Heart,
    Hexagon,
  },
  setup() {
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()

    const activeTab = ref('content')
    const elementData = ref({})
    const shadowColor = ref('#000000')
    const shadowBlur = ref(0)
    const shadowOffsetX = ref(0)
    const shadowOffsetY = ref(0)
    const filters = ref({
      brightness: 100,
      contrast: 100,
      saturation: 100,
      blur: 0,
    })

    const selectedElement = computed(() => canvasStore.selectedElement)

    // Available tabs based on element type
    const availableTabs = computed(() => {
      const baseTabs = [
        { id: 'content', name: 'Контент', icon: 'Type' },
        { id: 'style', name: 'Стиль', icon: 'Palette' },
        { id: 'transform', name: 'Трансформ', icon: 'Move' },
        { id: 'effects', name: 'Эффекты', icon: 'Sparkles' },
      ]

      return baseTabs
    })

    // Font options
    const fonts = [
      { name: 'Arial', value: 'Arial, sans-serif' },
      { name: 'Helvetica', value: 'Helvetica, sans-serif' },
      { name: 'Times New Roman', value: 'Times New Roman, serif' },
      { name: 'Georgia', value: 'Georgia, serif' },
      { name: 'Verdana', value: 'Verdana, sans-serif' },
      { name: 'Courier New', value: 'Courier New, monospace' },
      { name: 'Impact', value: 'Impact, sans-serif' },
    ]

    // Alignment options
    const alignments = [
      { value: 'left', icon: 'AlignLeft' },
      { value: 'center', icon: 'AlignCenter' },
      { value: 'right', icon: 'AlignRight' },
    ]

    // Shape types
    const shapeTypes = [
      { type: 'rectangle', icon: 'Square' },
      { type: 'circle', icon: 'Circle' },
      { type: 'triangle', icon: 'Triangle' },
      { type: 'star', icon: 'Star' },
      { type: 'heart', icon: 'Heart' },
      { type: 'hexagon', icon: 'Hexagon' },
    ]

    // Quick position grid (3x3)
    const quickPositions = [
      { style: { top: '2px', left: '2px' }, x: 0.1, y: 0.1 },
      { style: { top: '2px', left: '50%', transform: 'translateX(-50%)' }, x: 0.5, y: 0.1 },
      { style: { top: '2px', right: '2px' }, x: 0.9, y: 0.1 },
      { style: { top: '50%', left: '2px', transform: 'translateY(-50%)' }, x: 0.1, y: 0.5 },
      { style: { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }, x: 0.5, y: 0.5 },
      { style: { top: '50%', right: '2px', transform: 'translateY(-50%)' }, x: 0.9, y: 0.5 },
      { style: { bottom: '2px', left: '2px' }, x: 0.1, y: 0.9 },
      { style: { bottom: '2px', left: '50%', transform: 'translateX(-50%)' }, x: 0.5, y: 0.9 },
      { style: { bottom: '2px', right: '2px' }, x: 0.9, y: 0.9 },
    ]

    // Watch for element changes
    let previousElementId = null
    watch(
      selectedElement,
      (element, oldElement) => {
        if (element) {
          elementData.value = { ...element }

          // Initialize shadow values
          if (element.shadow) {
            const shadow = element.shadow
            shadowColor.value = shadow.color || '#000000'
            shadowBlur.value = shadow.blur || 0
            shadowOffsetX.value = shadow.offsetX || 0
            shadowOffsetY.value = shadow.offsetY || 0
          }

          // Initialize filter values
          if (element.filters) {
            filters.value = { ...element.filters }
          }

          // Set default tab only when selecting a different element or first time
          const isNewElement = !oldElement || oldElement.id !== element.id
          if (isNewElement) {
            if (element.type === 'text') {
              activeTab.value = 'content'
            } else {
              activeTab.value = 'style'
            }
          }
        }
      },
      { immediate: true },
    )

    const setActiveTab = (tabId) => {
      activeTab.value = tabId
      hapticFeedback.selectionChanged()
    }

    const updateElement = () => {
      if (selectedElement.value) {
        canvasStore.updateElement(selectedElement.value.id, elementData.value)
        hapticFeedback.impactOccurred('light')
      }
    }

    const toggleFontWeight = () => {
      elementData.value.fontWeight = elementData.value.fontWeight === 'bold' ? 'normal' : 'bold'
      updateElement()
    }

    const toggleFontStyle = () => {
      elementData.value.fontStyle = elementData.value.fontStyle === 'italic' ? 'normal' : 'italic'
      updateElement()
    }

    const setTextAlign = (align) => {
      elementData.value.textAlign = align
      updateElement()
    }

    const setShapeType = (type) => {
      elementData.value.shapeType = type
      updateElement()
    }

    const setQuickPosition = (pos) => {
      const canvasWidth = canvasStore.width
      const canvasHeight = canvasStore.height

      elementData.value.x = Math.round((canvasWidth - elementData.value.width) * pos.x)
      elementData.value.y = Math.round((canvasHeight - elementData.value.height) * pos.y)
      updateElement()
    }

    const updateShadow = () => {
      elementData.value.shadow = {
        color: shadowColor.value,
        blur: shadowBlur.value,
        offsetX: shadowOffsetX.value,
        offsetY: shadowOffsetY.value,
      }
      updateElement()
    }

    const updateFilters = () => {
      elementData.value.filters = { ...filters.value }
      updateElement()
    }

    const changeImage = () => {
      // Trigger file input or image selection
      console.log('Change image')
    }

    return {
      activeTab,
      elementData,
      shadowColor,
      shadowBlur,
      shadowOffsetX,
      shadowOffsetY,
      filters,
      selectedElement,
      availableTabs,
      fonts,
      alignments,
      shapeTypes,
      quickPositions,
      setActiveTab,
      updateElement,
      toggleFontWeight,
      toggleFontStyle,
      setTextAlign,
      setShapeType,
      setQuickPosition,
      updateShadow,
      updateFilters,
      changeImage,
    }
  },
}
</script>

<style lang="scss" scoped>
.unified-editor {
  background: var(--tg-theme-secondary-bg-color, #ffffff);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  animation: slideUpFade 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUpFade {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.editor-tabs {
  display: flex;
  background: var(--tg-theme-bg-color, #f8fafc);
  border-bottom: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: var(--tg-theme-hint-color, #64748b);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;

  &:hover {
    background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
    color: var(--tg-theme-text-color, #475569);
  }

  &.active {
    background: var(--tg-theme-secondary-bg-color, #ffffff);
    color: var(--tg-theme-button-color, #667eea);
    box-shadow: 0 -2px 0 var(--tg-theme-button-color, #667eea) inset;
  }

  span {
    display: none;

    @media (min-width: 480px) {
      display: inline;
    }
  }
}

.editor-content {
  max-height: 50vh;
  overflow-y: auto;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
}

.tab-content {
  padding: 20px;
}

.content-section,
.style-section,
.transform-section,
.effects-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.style-group,
.transform-group,
.effect-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--tg-theme-bg-color, #f8fafc);
  border-radius: 12px;
  border: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.05));
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--tg-theme-button-color, rgba(102, 126, 234, 0.3));
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  label {
    font-size: 14px;
    font-weight: 600;
    color: var(--tg-theme-text-color, #374151);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.text-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: #667eea;
  }
}

.font-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;

  &:focus {
    outline: none;
    border-color: #667eea;
  }
}

.slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  outline: none;
  transition: all 0.3s ease;

  &:hover {
    background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.15));
  }

  &::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--tg-theme-button-color, #667eea);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.style-buttons,
.align-buttons {
  display: flex;
  gap: 8px;
}

.style-btn,
.align-btn {
  width: 44px;
  height: 44px;
  border: 2px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  border-radius: 12px;
  background: var(--tg-theme-secondary-bg-color, #ffffff);
  color: var(--tg-theme-hint-color, #6b7280);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--tg-theme-button-color, #667eea);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: var(--tg-theme-button-color, #667eea);
    color: var(--tg-theme-button-color, #667eea);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    &::before {
      opacity: 0.1;
    }
  }

  &.active {
    border-color: var(--tg-theme-button-color, #667eea);
    background: var(--tg-theme-button-color, #667eea);
    color: var(--tg-theme-button-text-color, #ffffff);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--tg-theme-button-color, 102, 126, 234), 0.3);

    &::before {
      opacity: 0;
    }
  }

  &:active {
    transform: translateY(0);
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

.color-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  background: none;

  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  &::-webkit-color-swatch {
    border: none;
    border-radius: 6px;
  }
}

.color-input {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Courier New', monospace;

  &:focus {
    outline: none;
    border-color: #667eea;
  }
}

.stroke-controls {
  display: flex;
  gap: 8px;
  align-items: center;

  .slider {
    flex: 1;
  }

  span {
    font-size: 12px;
    color: #6b7280;
    min-width: 40px;
  }
}

.position-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  width: 120px;
  height: 120px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px;
  background: #f9fafb;
}

.position-btn {
  position: relative;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.3s ease;

  &:hover {
    background: rgba(102, 126, 234, 0.1);
  }
}

.position-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
  transition: all 0.3s ease;

  .position-btn:hover & {
    background: #4f46e5;
    transform: scale(1.2);
  }
}

.input-row {
  display: flex;
  gap: 12px;
}

.input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;

  label {
    font-size: 12px;
    color: #6b7280;
  }
}

.number-input {
  width: 100%;
  padding: 8px 10px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;

  &:focus {
    outline: none;
    border-color: #667eea;
  }
}

.image-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;

  img {
    max-width: 120px;
    max-height: 120px;
    border-radius: 8px;
    object-fit: cover;
  }
}

.change-image-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    color: #667eea;
  }
}

.shape-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.shape-option {
  width: 44px;
  height: 44px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    color: #667eea;
  }

  &.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
  }
}

.shadow-controls,
.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;

  label {
    font-size: 12px;
    color: #6b7280;
  }
}

@media (max-width: 480px) {
  .tab-content {
    padding: 16px;
  }

  .input-row {
    flex-direction: column;
    gap: 8px;
  }

  .style-buttons,
  .align-buttons {
    justify-content: center;
  }

  .position-grid {
    width: 100px;
    height: 100px;
  }
}
</style>

<template>
  <div class="bottom-toolbar">
    <!-- Main toolbar -->
    <div class="toolbar-main">
      <button
        v-for="tool in tools"
        :key="tool.id"
        class="tool-btn"
        :class="{ active: activeTool === tool.id }"
        @click="handleToolClick(tool.id)"
      >
        <component :is="tool.icon" :size="24" />
        <span>{{ tool.name }}</span>
      </button>
    </div>

    <!-- Tool panels -->
    <div class="tool-panels" v-if="activeTool || selectedElement">
      <!-- Element Editor Panel (shows when element is selected) -->
      <div v-if="selectedElement && !activeTool" class="tool-panel">
        <div class="panel-header">
          <h3>
            <component :is="getElementIcon(selectedElement.type)" :size="20" />
            Редактор элементов
          </h3>
          <button class="close-btn" @click="deselectElement">
            <X :size="18" />
          </button>
        </div>
        <div class="panel-content">
          <UnifiedElementEditor />
        </div>
      </div>

      <!-- Stickers Panel -->
      <div v-if="activeTool === 'stickers'" class="tool-panel">
        <div class="panel-header">
          <h3>
            <Smile :size="20" />
            Стикеры
          </h3>
          <button class="close-btn" @click="closeTool">
            <X :size="18" />
          </button>
        </div>
        <div class="panel-content">
          <StickerPanel :is-visible="true" @close="closeTool" />
        </div>
      </div>

      <!-- Shapes Panel -->
      <div v-if="activeTool === 'shapes'" class="tool-panel">
        <div class="panel-header">
          <h3>
            <Square :size="20" />
            Фигуры
          </h3>
          <button class="close-btn" @click="closeTool">
            <X :size="18" />
          </button>
        </div>
        <div class="panel-content">
          <ShapesPanel @close="closeTool" />
        </div>
      </div>

      <!-- Background Panel -->
      <div v-if="activeTool === 'background'" class="tool-panel">
        <div class="panel-header">
          <h3>
            <Palette :size="20" />
            Фон
          </h3>
          <button class="close-btn" @click="closeTool">
            <X :size="18" />
          </button>
        </div>
        <div class="panel-content">
          <BackgroundPanel @close="closeTool" />
        </div>
      </div>

      <!-- Images Panel -->
      <div v-if="activeTool === 'images'" class="tool-panel">
        <div class="panel-header">
          <h3>
            <ImageIcon :size="20" />
            Изображения
          </h3>
          <button class="close-btn" @click="closeTool">
            <X :size="18" />
          </button>
        </div>
        <div class="panel-content">
          <ImagesPanel @close="closeTool" />
        </div>
      </div>

      <!-- Export Panel -->
      <div v-if="activeTool === 'export'" class="tool-panel">
        <div class="panel-header">
          <h3>
            <Download :size="20" />
            Экспорт
          </h3>
          <button class="close-btn" @click="closeTool">
            <X :size="18" />
          </button>
        </div>
        <div class="panel-content">
          <ExportPanel :canvas-ref="canvasRef" @close="closeTool" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import { Type, Smile, Square, Palette, ImageIcon, Download, X } from 'lucide-vue-next'

// Import panels
import UnifiedElementEditor from './UnifiedElementEditor.vue'
import StickerPanel from './StickerPanel.vue'
import ShapesPanel from './ShapesPanel.vue'
import BackgroundPanel from './BackgroundPanel.vue'
import ImagesPanel from './ImagesPanel.vue'
import ExportPanel from './ExportPanel.vue'

export default {
  name: 'BottomToolbar',
  props: {
    canvasRef: {
      type: Object,
      default: null,
    },
  },
  components: {
    Type,
    Smile,
    Square,
    Palette,
    ImageIcon,
    Download,
    X,
    UnifiedElementEditor,
    StickerPanel,
    ShapesPanel,
    BackgroundPanel,
    ImagesPanel,
    ExportPanel,
  },
  emits: ['tool-changed'],
  setup(props, { emit }) {
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()

    const activeTool = ref(null)
    const selectedElement = computed(() => canvasStore.selectedElement)

    const tools = [
      { id: 'text', name: 'Текст', icon: 'Type' },
      { id: 'stickers', name: 'Стикеры', icon: 'Smile' },
      { id: 'shapes', name: 'Фигуры', icon: 'Square' },
      { id: 'background', name: 'Фон', icon: 'Palette' },
      { id: 'images', name: 'Фото', icon: 'ImageIcon' },
      { id: 'export', name: 'Экспорт', icon: 'Download' },
    ]

    const handleToolClick = (toolId) => {
      if (activeTool.value === toolId) {
        activeTool.value = null
      } else {
        activeTool.value = toolId

        // Auto-add element when clicking certain tools
        if (toolId === 'text') {
          addTextElement()
        }
      }

      hapticFeedback.selectionChanged()
      emit('tool-changed', activeTool.value)
    }

    const addTextElement = () => {
      const element = {
        type: 'text',
        text: 'Новый текст',
        x: 50,
        y: 50,
        width: 200,
        height: 50,
        fontSize: 32,
        fontFamily: 'Arial, sans-serif',
        fontWeight: 'normal',
        fontStyle: 'normal',
        color: '#000000',
        textAlign: 'left',
        opacity: 1,
        rotation: 0,
      }

      canvasStore.addElement(element)
      hapticFeedback.impactOccurred('medium')
    }

    const getElementIcon = (type) => {
      const icons = {
        text: 'Type',
        image: 'ImageIcon',
        shape: 'Square',
        sticker: 'Smile',
      }
      return icons[type] || 'Square'
    }

    const deselectElement = () => {
      canvasStore.selectElement(null)
      hapticFeedback.impactOccurred('light')
    }

    const closeTool = () => {
      activeTool.value = null
      hapticFeedback.impactOccurred('light')
      emit('tool-changed', null)
    }

    return {
      activeTool,
      selectedElement,
      tools,
      handleToolClick,
      getElementIcon,
      deselectElement,
      closeTool,
    }
  },
}
</script>

<style lang="scss" scoped>
.bottom-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--tg-theme-secondary-bg-color, #ffffff);
  border-top: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  z-index: 100;

  // Safe area for mobile devices
  padding-bottom: env(safe-area-inset-bottom);
}

.toolbar-main {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 16px;
  background: white;
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: var(--tg-theme-hint-color, #6b7280);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;

  &:hover {
    background: var(--tg-theme-bg-color, #f3f4f6);
    color: var(--tg-theme-text-color, #374151);
  }

  &.active {
    background: var(--tg-theme-button-color, #667eea);
    color: var(--tg-theme-button-text-color, #ffffff);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--tg-theme-button-color, 102, 126, 234), 0.3);
  }

  span {
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
  }
}

.tool-panels {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--tg-theme-secondary-bg-color, #ffffff);
  border-top: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.tool-panel {
  max-height: 60vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
  background: var(--tg-theme-bg-color, #fafafa);

  h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--tg-theme-text-color, #1f2937);
  }

  .close-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: transparent;
    color: var(--tg-theme-hint-color, #6b7280);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: var(--tg-theme-hint-color, rgba(0, 0, 0, 0.1));
      color: var(--tg-theme-text-color, #374151);
    }
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;

  // Remove any overflow hidden from child components
  :deep(.text-editor-tabs),
  :deep(.sticker-panel),
  :deep(.shapes-panel),
  :deep(.background-panel),
  :deep(.images-panel),
  :deep(.export-panel) {
    overflow: visible;
    max-height: none;
    height: auto;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
}

@media (max-width: 768px) {
  .toolbar-main {
    padding: 8px 12px;
  }

  .tool-btn {
    padding: 6px 8px;
    min-width: 50px;

    span {
      font-size: 10px;
    }
  }

  .tool-panels {
    max-height: 50vh;
  }

  .panel-header {
    padding: 12px 16px;

    h3 {
      font-size: 14px;
    }
  }
}
</style>

<template>
  <div class="onboarding-slides" v-if="isVisible">
    <div class="slides-container">
      <!-- Progress indicator -->
      <div class="progress-bar">
        <div
          v-for="(slide, index) in slides"
          :key="index"
          class="progress-dot"
          :class="{ active: index === currentSlide, completed: index < currentSlide }"
        ></div>
      </div>

      <!-- Slide 1: Project Type -->
      <div v-if="currentSlide === 0" class="slide slide--project-type">
        <div class="slide__header">
          <h1>Создать новый проект</h1>
          <p>Выберите тип проекта для начала работы</p>
        </div>

        <div class="project-types">
          <button
            v-for="type in projectTypes"
            :key="type.id"
            class="project-type-card"
            :class="{ active: selectedProjectType === type.id }"
            @click="selectProjectType(type.id)"
          >
            <div class="project-type-card__icon">
              <component :is="type.icon" :size="32" />
            </div>
            <h3>{{ type.name }}</h3>
            <p>{{ type.description }}</p>
            <div class="project-type-card__badge">{{ type.category }}</div>
          </button>
        </div>
      </div>

      <!-- Slide 2: Canvas Size -->
      <div v-if="currentSlide === 1" class="slide slide--canvas-size">
        <div class="slide__header">
          <h1>Размер холста</h1>
          <p>Выберите размер для вашего проекта</p>
        </div>

        <div class="size-options">
          <!-- Preset sizes -->
          <div class="preset-sizes">
            <h3>Готовые размеры</h3>
            <div class="preset-grid">
              <button
                v-for="preset in canvasPresets"
                :key="preset.id"
                class="preset-card"
                :class="{ active: selectedPreset === preset.id }"
                @click="selectPreset(preset)"
              >
                <div class="preset-card__preview">
                  <div
                    class="preview-rect"
                    :style="{
                      aspectRatio: `${preset.width} / ${preset.height}`,
                      background: preset.color,
                    }"
                  ></div>
                </div>
                <div class="preset-card__info">
                  <h4>{{ preset.name }}</h4>
                  <span>{{ preset.width }} × {{ preset.height }}</span>
                </div>
              </button>
            </div>
          </div>

          <!-- Custom size -->
          <div class="custom-size">
            <h3>Пользовательский размер</h3>
            <div class="size-inputs">
              <div class="input-group">
                <label>Ширина</label>
                <input
                  type="number"
                  v-model="customWidth"
                  min="100"
                  max="4000"
                  @input="selectCustomSize"
                />
                <span>px</span>
              </div>
              <div class="input-group">
                <label>Высота</label>
                <input
                  type="number"
                  v-model="customHeight"
                  min="100"
                  max="4000"
                  @input="selectCustomSize"
                />
                <span>px</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Slide 3: Background -->
      <div v-if="currentSlide === 2" class="slide slide--background">
        <div class="slide__header">
          <h1>Фон холста</h1>
          <p>Выберите фон для вашего проекта</p>
        </div>

        <div class="background-options">
          <!-- Solid colors -->
          <div class="bg-section">
            <h3>Цвета</h3>
            <div class="color-grid">
              <button
                v-for="color in backgroundColors"
                :key="color"
                class="color-option"
                :class="{ active: selectedBackground === color }"
                :style="{ backgroundColor: color }"
                @click="selectBackground(color)"
              ></button>
            </div>
          </div>

          <!-- Gradients -->
          <div class="bg-section">
            <h3>Градиенты</h3>
            <div class="gradient-grid">
              <button
                v-for="gradient in backgroundGradients"
                :key="gradient.id"
                class="gradient-option"
                :class="{ active: selectedBackground === gradient.value }"
                :style="{ background: gradient.value }"
                @click="selectBackground(gradient.value)"
              ></button>
            </div>
          </div>

          <!-- Transparent -->
          <div class="bg-section">
            <h3>Прозрачность</h3>
            <button
              class="transparent-option"
              :class="{ active: selectedBackground === 'transparent' }"
              @click="selectBackground('transparent')"
            >
              <div class="transparent-pattern"></div>
              <span>Прозрачный</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="slide-navigation">
        <button v-if="currentSlide > 0" class="nav-btn nav-btn--back" @click="previousSlide">
          <ChevronLeft :size="20" />
          Назад
        </button>

        <button
          v-if="currentSlide < slides.length - 1"
          class="nav-btn nav-btn--next"
          :disabled="!canProceed"
          @click="nextSlide"
        >
          Далее
          <ChevronRight :size="20" />
        </button>

        <button
          v-if="currentSlide === slides.length - 1"
          class="nav-btn nav-btn--create"
          :disabled="!canProceed"
          @click="createProject"
        >
          <Plus :size="20" />
          Создать проект
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useTelegram } from '@/composables/useTelegram'
import {
  ChevronLeft,
  ChevronRight,
  Plus,
  Instagram,
  Youtube,
  Facebook,
  Twitter,
  Linkedin,
  FileText,
  Image,
  Presentation,
  Smartphone,
  Monitor,
} from 'lucide-vue-next'

export default {
  name: 'OnboardingSlides',
  components: {
    ChevronLeft,
    ChevronRight,
    Plus,
    Instagram,
    Youtube,
    Facebook,
    Twitter,
    Linkedin,
    FileText,
    Image,
    Presentation,
    Smartphone,
    Monitor,
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['close', 'project-created'],
  setup(props, { emit }) {
    const { hapticFeedback } = useTelegram()

    const currentSlide = ref(0)
    const selectedProjectType = ref(null)
    const selectedPreset = ref(null)
    const customWidth = ref(800)
    const customHeight = ref(600)
    const selectedBackground = ref('#ffffff')

    const slides = ['project-type', 'canvas-size', 'background']

    // Project types
    const projectTypes = [
      {
        id: 'social-post',
        name: 'Пост в соцсети',
        description: 'Instagram, Facebook, VK',
        icon: 'Instagram',
        category: 'Социальные сети',
      },
      {
        id: 'story',
        name: 'История/Stories',
        description: 'Вертикальный формат',
        icon: 'Smartphone',
        category: 'Социальные сети',
      },
      {
        id: 'youtube-thumbnail',
        name: 'Превью YouTube',
        description: 'Обложка для видео',
        icon: 'Youtube',
        category: 'Видео',
      },
      {
        id: 'presentation',
        name: 'Презентация',
        description: 'Слайд для презентации',
        icon: 'Presentation',
        category: 'Бизнес',
      },
      {
        id: 'banner',
        name: 'Баннер',
        description: 'Рекламный баннер',
        icon: 'Monitor',
        category: 'Реклама',
      },
      {
        id: 'custom',
        name: 'Свой проект',
        description: 'Произвольный размер',
        icon: 'FileText',
        category: 'Другое',
      },
    ]

    // Canvas presets based on project type
    const canvasPresets = computed(() => {
      const presets = {
        'social-post': [
          {
            id: 'instagram-post',
            name: 'Instagram пост',
            width: 1080,
            height: 1080,
            color: '#E1306C',
          },
          {
            id: 'facebook-post',
            name: 'Facebook пост',
            width: 1200,
            height: 630,
            color: '#1877F2',
          },
          { id: 'twitter-post', name: 'Twitter пост', width: 1024, height: 512, color: '#1DA1F2' },
        ],
        story: [
          {
            id: 'instagram-story',
            name: 'Instagram Stories',
            width: 1080,
            height: 1920,
            color: '#E1306C',
          },
          {
            id: 'facebook-story',
            name: 'Facebook Stories',
            width: 1080,
            height: 1920,
            color: '#1877F2',
          },
        ],
        'youtube-thumbnail': [
          {
            id: 'youtube-thumb',
            name: 'YouTube превью',
            width: 1280,
            height: 720,
            color: '#FF0000',
          },
        ],
        presentation: [
          { id: 'slide-16-9', name: 'Слайд 16:9', width: 1920, height: 1080, color: '#4285F4' },
          { id: 'slide-4-3', name: 'Слайд 4:3', width: 1024, height: 768, color: '#4285F4' },
        ],
        banner: [
          { id: 'banner-wide', name: 'Широкий баннер', width: 1200, height: 300, color: '#34A853' },
          {
            id: 'banner-square',
            name: 'Квадратный баннер',
            width: 600,
            height: 600,
            color: '#34A853',
          },
        ],
        custom: [
          { id: 'custom-800-600', name: 'Стандартный', width: 800, height: 600, color: '#6B7280' },
        ],
      }

      return presets[selectedProjectType.value] || presets['custom']
    })

    // Background options
    const backgroundColors = [
      '#ffffff',
      '#f8f9fa',
      '#e9ecef',
      '#dee2e6',
      '#ced4da',
      '#000000',
      '#212529',
      '#343a40',
      '#495057',
      '#6c757d',
      '#007bff',
      '#6610f2',
      '#6f42c1',
      '#e83e8c',
      '#dc3545',
      '#fd7e14',
      '#ffc107',
      '#28a745',
      '#20c997',
      '#17a2b8',
    ]

    const backgroundGradients = [
      { id: 'sunset', value: 'linear-gradient(135deg, #ff6b6b, #feca57)' },
      { id: 'ocean', value: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { id: 'forest', value: 'linear-gradient(135deg, #11998e, #38ef7d)' },
      { id: 'purple', value: 'linear-gradient(135deg, #667eea, #764ba2)' },
      { id: 'pink', value: 'linear-gradient(135deg, #f093fb, #f5576c)' },
      { id: 'blue', value: 'linear-gradient(135deg, #4facfe, #00f2fe)' },
    ]

    // Computed
    const canProceed = computed(() => {
      switch (currentSlide.value) {
        case 0:
          return selectedProjectType.value !== null
        case 1:
          return selectedPreset.value !== null || (customWidth.value && customHeight.value)
        case 2:
          return selectedBackground.value !== null
        default:
          return false
      }
    })

    // Methods
    const selectProjectType = (typeId) => {
      selectedProjectType.value = typeId
      selectedPreset.value = null // Reset preset when changing project type
      hapticFeedback.selectionChanged()
    }

    const selectPreset = (preset) => {
      selectedPreset.value = preset.id
      customWidth.value = preset.width
      customHeight.value = preset.height
      hapticFeedback.selectionChanged()
    }

    const selectCustomSize = () => {
      selectedPreset.value = 'custom'
      hapticFeedback.selectionChanged()
    }

    const selectBackground = (background) => {
      selectedBackground.value = background
      hapticFeedback.selectionChanged()
    }

    const nextSlide = () => {
      if (currentSlide.value < slides.length - 1) {
        currentSlide.value++
        hapticFeedback.impactOccurred('light')
      }
    }

    const previousSlide = () => {
      if (currentSlide.value > 0) {
        currentSlide.value--
        hapticFeedback.impactOccurred('light')
      }
    }

    const createProject = () => {
      const projectData = {
        type: selectedProjectType.value,
        width: customWidth.value,
        height: customHeight.value,
        background: selectedBackground.value,
      }

      emit('project-created', projectData)
      hapticFeedback.notificationOccurred('success')
    }

    return {
      currentSlide,
      selectedProjectType,
      selectedPreset,
      customWidth,
      customHeight,
      selectedBackground,
      slides,
      projectTypes,
      canvasPresets,
      backgroundColors,
      backgroundGradients,
      canProceed,
      selectProjectType,
      selectPreset,
      selectCustomSize,
      selectBackground,
      nextSlide,
      previousSlide,
      createProject,
    }
  },
}
</script>

<style lang="scss" scoped>
.onboarding-slides {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.slides-container {
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.progress-bar {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 24px 24px 0;
}

.progress-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e5e7eb;
  transition: all 0.3s ease;

  &.active {
    background: #667eea;
    transform: scale(1.2);
  }

  &.completed {
    background: #10b981;
  }
}

.slide {
  padding: 32px;
  min-height: 500px;

  &__header {
    text-align: center;
    margin-bottom: 40px;

    h1 {
      margin: 0 0 12px;
      font-size: 32px;
      font-weight: 700;
      color: #1f2937;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      margin: 0;
      font-size: 18px;
      color: #6b7280;
    }
  }
}

.project-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.project-type-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #667eea;
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
  }

  &.active {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
  }

  &__icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  h3 {
    margin: 0 0 8px;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
  }

  p {
    margin: 0 0 16px;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
  }

  &__badge {
    display: inline-block;
    padding: 4px 12px;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
  }
}

.size-options {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.preset-sizes h3,
.custom-size h3 {
  margin: 0 0 20px;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.preset-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;

  &:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  }

  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }

  &__preview {
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
  }

  .preview-rect {
    width: 60px;
    max-height: 40px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  &__info h4 {
    margin: 0 0 4px;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
  }

  &__info span {
    font-size: 12px;
    color: #6b7280;
  }
}

.custom-size {
  .size-inputs {
    display: flex;
    gap: 20px;
    justify-content: center;
  }

  .input-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    label {
      font-size: 14px;
      font-weight: 500;
      color: #6b7280;
    }

    input {
      width: 100px;
      padding: 12px;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #667eea;
      }
    }

    span {
      font-size: 12px;
      color: #6b7280;
    }
  }
}

.background-options {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.bg-section h3 {
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
  gap: 12px;
  max-width: 600px;
}

.color-option {
  width: 50px;
  height: 50px;
  border: 3px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: scale(1.1);
  }

  &.active {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
  }

  &::after {
    content: '';
    position: absolute;
    inset: 4px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}

.gradient-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  max-width: 600px;
}

.gradient-option {
  width: 100px;
  height: 60px;
  border: 3px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &.active {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
  }
}

.transparent-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: 200px;

  &:hover {
    border-color: #667eea;
    transform: translateY(-2px);
  }

  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }

  .transparent-pattern {
    width: 40px;
    height: 40px;
    background-image:
      linear-gradient(45deg, #f3f4f6 25%, transparent 25%),
      linear-gradient(-45deg, #f3f4f6 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f3f4f6 75%),
      linear-gradient(-45deg, transparent 75%, #f3f4f6 75%);
    background-size: 8px 8px;
    background-position:
      0 0,
      0 4px,
      4px -4px,
      -4px 0px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
  }
}

.slide-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-top: 1px solid #f3f4f6;
  margin-top: 32px;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }

  &--back {
    background: #f3f4f6;
    color: #6b7280;

    &:hover:not(:disabled) {
      background: #e5e7eb;
      transform: translateX(-2px);
    }
  }

  &--next,
  &--create {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
  }
}

@media (max-width: 768px) {
  .onboarding-slides {
    padding: 12px;
  }

  .slides-container {
    border-radius: 16px;
    max-height: 95vh;
  }

  .slide {
    padding: 24px 20px;
    min-height: 400px;

    &__header h1 {
      font-size: 24px;
    }

    &__header p {
      font-size: 16px;
    }
  }

  .project-types {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .project-type-card {
    padding: 20px;

    &__icon {
      width: 56px;
      height: 56px;
    }
  }

  .preset-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .custom-size .size-inputs {
    flex-direction: column;
    gap: 16px;
  }

  .color-grid {
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  }

  .color-option {
    width: 40px;
    height: 40px;
  }

  .gradient-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }

  .gradient-option {
    width: 80px;
    height: 50px;
  }

  .slide-navigation {
    padding: 20px;
    flex-direction: column;
    gap: 12px;

    .nav-btn {
      width: 100%;
      justify-content: center;
    }
  }
}
</style>

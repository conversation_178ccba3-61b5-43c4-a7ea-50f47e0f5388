# Post Generator Telegram Bot

Telegram inline-бот для быстрой генерации визуальных постов с цитатами.

## Возможности

- 🎨 **Inline режим**: Быстрое создание постов с цитатами прямо в чате
- 📱 **WebApp**: Полнофункциональный редактор для создания сложных постов
- 🎯 **4 готовых шаблона** для цитат
- 🖼️ **Высокое качество** изображений (800x600px)
- 🌐 **Поддержка RU/EN** языков

## Установка и настройка

### 1. Создание бота

1. Напишите [@BotFather](https://t.me/BotFather) в Telegram
2. Создайте нового бота командой `/newbot`
3. Получите токен бота
4. Включите inline режим командой `/setinline`
5. Настройте WebApp командой `/setmenubutton`

### 2. Установка зависимостей

```bash
cd bot
npm install
```

### 3. Настройка окружения

```bash
cp .env.example .env
```

Отредактируйте `.env` файл:

```env
BOT_TOKEN=your_bot_token_here
WEBAPP_URL=https://your-domain.vercel.app
PORT=3000
```

### 4. Запуск

**Разработка:**
```bash
npm run dev
```

**Продакшен:**
```bash
npm start
```

## Использование

### Inline режим

В любом чате Telegram введите:
```
@your_bot_username "Ваша цитата"
```

Выберите один из предложенных шаблонов и отправьте в чат.

### WebApp редактор

1. Напишите боту `/start`
2. Нажмите "🎨 Открыть редактор"
3. Создавайте посты с полной настройкой

## Шаблоны

Бот включает 4 готовых шаблона:

1. **Синий градиент** - элегантный градиент с белым текстом
2. **Оранжевый градиент** - яркий теплый градиент
3. **Темный фон** - минималистичный темный дизайн
4. **Светлый минимализм** - чистый светлый фон

## Деплой

### Heroku

1. Создайте приложение на Heroku
2. Добавьте переменные окружения
3. Подключите GitHub репозиторий
4. Деплойте

### Railway

1. Подключите GitHub репозиторий
2. Настройте переменные окружения
3. Деплойте

### VPS

1. Клонируйте репозиторий
2. Установите Node.js и npm
3. Настройте PM2 или systemd
4. Запустите бота

## Структура проекта

```
bot/
├── index.js          # Основной файл бота
├── package.json      # Зависимости
├── .env.example      # Пример настроек
└── README.md         # Документация
```

## API

### Inline Query

Бот обрабатывает inline запросы и возвращает до 4 вариантов изображений с цитатой.

**Формат запроса:**
```
@bot_username текст цитаты
```

**Ответ:**
- Массив изображений в разных стилях
- Каждое изображение содержит переданный текст
- Подпись с информацией о генераторе

### Commands

- `/start` - Главное меню с кнопкой WebApp
- `/help` - Справка по использованию

## Разработка

### Добавление новых шаблонов

Отредактируйте массив `QUOTE_TEMPLATES` в `index.js`:

```javascript
{
  id: 'unique-id',
  name: 'Название шаблона',
  background: { 
    type: 'gradient', // или 'color'
    colors: ['#start', '#end'] // для градиента
    // color: '#ffffff' // для сплошного цвета
  },
  textStyle: {
    fontSize: 36,
    fontFamily: 'Arial',
    color: '#ffffff',
    textAlign: 'center',
    shadow: { 
      color: 'rgba(0,0,0,0.3)', 
      blur: 4, 
      offsetX: 2, 
      offsetY: 2 
    }
  }
}
```

### Настройка Canvas

Для изменения размера изображений отредактируйте переменные `width` и `height` в функции `generateQuoteImage()`.

## Лицензия

MIT License

## Поддержка

Если у вас есть вопросы или предложения, создайте issue в GitHub репозитории.

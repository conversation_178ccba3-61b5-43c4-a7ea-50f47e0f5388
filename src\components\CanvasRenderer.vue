<template>
  <div class="canvas-container" ref="containerRef">
    <canvas
      ref="canvasRef"
      :width="canvasWidth"
      :height="canvasHeight"
      :style="{
        width: displayWidth + 'px',
        height: displayHeight + 'px',
      }"
      :class="{ dragging: isDragging }"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    />

    <!-- Canvas Guides -->
    <CanvasGuides :show-guides="isDragging" :snap-threshold="snapThreshold" />

    <!-- Loading overlay -->
    <div v-if="isLoading" class="canvas-loading">
      <div class="spinner">
        <div class="spinner__circle"></div>
      </div>
      <p>{{ loadingText }}</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useCanvasStore } from '@/stores/canvas'
import { useTelegram } from '@/composables/useTelegram'
import CanvasGuides from './CanvasGuides.vue'

export default {
  name: 'CanvasRenderer',
  components: {
    CanvasGuides,
  },
  props: {
    width: {
      type: Number,
      default: 800,
    },
    height: {
      type: Number,
      default: 600,
    },
  },
  emits: ['element-selected', 'element-moved', 'canvas-updated'],
  setup(props, { emit }) {
    const canvasRef = ref(null)
    const containerRef = ref(null)
    const canvasStore = useCanvasStore()
    const { hapticFeedback } = useTelegram()

    // Canvas state
    const canvasWidth = ref(props.width)
    const canvasHeight = ref(props.height)
    const isLoading = ref(false)
    const loadingText = ref('')
    const containerSize = ref({ width: 0, height: 0 })

    // Canvas scaling
    const canvasScale = computed(() => {
      if (!containerSize.value.width || !containerSize.value.height) return 1

      const containerAspect = containerSize.value.width / containerSize.value.height
      const canvasAspect = canvasWidth.value / canvasHeight.value

      // Calculate scale to fit canvas in container with padding
      const padding = 40 // 20px padding on each side
      const availableWidth = containerSize.value.width - padding
      const availableHeight = containerSize.value.height - padding

      let scale
      if (canvasAspect > containerAspect) {
        // Canvas is wider than container
        scale = availableWidth / canvasWidth.value
      } else {
        // Canvas is taller than container
        scale = availableHeight / canvasHeight.value
      }

      // Limit scale to reasonable bounds
      return Math.min(Math.max(scale, 0.1), 2)
    })

    const displayWidth = computed(() => canvasWidth.value * canvasScale.value)
    const displayHeight = computed(() => canvasHeight.value * canvasScale.value)

    // Interaction state
    const isDragging = ref(false)
    const dragStartPos = ref({ x: 0, y: 0 })
    const selectedElement = ref(null)
    const lastTouchTime = ref(0)
    const snapThreshold = ref(10)

    let ctx = null
    let animationFrameId = null

    // Initialize canvas
    onMounted(async () => {
      await nextTick()
      initCanvas()
      setupResizeObserver()
      render()
    })

    onUnmounted(() => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
      }
    })

    // Watch for canvas store changes
    watch(
      () => canvasStore.elements,
      () => {
        render()
      },
      { deep: true },
    )

    watch(
      () => canvasStore.background,
      () => {
        render()
      },
      { deep: true },
    )

    // Initialize canvas context
    const initCanvas = () => {
      if (!canvasRef.value) return

      ctx = canvasRef.value.getContext('2d')
      ctx.imageSmoothingEnabled = true
      ctx.imageSmoothingQuality = 'high'

      // Set canvas size based on container
      updateCanvasSize()
    }

    // Update canvas size
    const updateCanvasSize = () => {
      if (!containerRef.value || !canvasRef.value) return

      const container = containerRef.value
      const rect = container.getBoundingClientRect()

      // Calculate optimal size maintaining aspect ratio
      const aspectRatio = props.width / props.height
      let newWidth = rect.width
      let newHeight = rect.width / aspectRatio

      if (newHeight > rect.height) {
        newHeight = rect.height
        newWidth = rect.height * aspectRatio
      }

      canvasWidth.value = Math.floor(newWidth)
      canvasHeight.value = Math.floor(newHeight)

      // Update canvas store dimensions
      canvasStore.setDimensions(canvasWidth.value, canvasHeight.value)
    }

    // Setup resize observer
    const setupResizeObserver = () => {
      if (!containerRef.value) return

      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect
          containerSize.value = { width, height }
        }
        updateCanvasSize()
        render()
      })

      resizeObserver.observe(containerRef.value)

      onUnmounted(() => {
        resizeObserver.disconnect()
      })
    }

    // Main render function
    const render = () => {
      if (!ctx) return

      // Clear canvas
      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

      // Render background
      renderBackground()

      // Render elements
      canvasStore.elements.forEach((element) => {
        renderElement(element)
      })

      emit('canvas-updated')
    }

    // Render background
    const renderBackground = () => {
      const bg = canvasStore.background

      if (bg.type === 'transparent') {
        // Don't fill anything for transparent background
        return
      } else if (bg.type === 'color') {
        ctx.fillStyle = bg.value
        ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)
      } else if (bg.type === 'gradient') {
        if (bg.value) {
          // Parse CSS gradient and apply it
          renderCSSGradient(bg.value)
        } else if (bg.colors) {
          // Legacy gradient format
          renderGradient(bg)
        }
      } else if (bg.type === 'image' && bg.image) {
        renderBackgroundImage(bg.image)
      }
    }

    // Render CSS gradient background
    const renderCSSGradient = (gradientCSS) => {
      // Create a temporary element to parse the gradient
      const tempDiv = document.createElement('div')
      tempDiv.style.background = gradientCSS
      tempDiv.style.width = '100px'
      tempDiv.style.height = '100px'
      document.body.appendChild(tempDiv)

      // Create canvas gradient based on CSS
      const gradient = ctx.createLinearGradient(0, 0, canvasWidth.value, canvasHeight.value)

      // Simple gradient parsing for common cases
      if (gradientCSS.includes('linear-gradient')) {
        // Extract colors from the gradient string
        const colorMatches = gradientCSS.match(
          /#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\([^)]+\)|rgba\([^)]+\)/g,
        )
        if (colorMatches && colorMatches.length >= 2) {
          colorMatches.forEach((color, index) => {
            gradient.addColorStop(index / (colorMatches.length - 1), color)
          })
        }
      }

      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)

      // Clean up
      document.body.removeChild(tempDiv)
    }

    // Render gradient background
    const renderGradient = (bg) => {
      const gradient = ctx.createLinearGradient(0, 0, canvasWidth.value, canvasHeight.value)
      bg.colors.forEach((color, index) => {
        gradient.addColorStop(index / (bg.colors.length - 1), color)
      })
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvasWidth.value, canvasHeight.value)
    }

    // Render background image
    const renderBackgroundImage = (image) => {
      const scale = Math.max(canvasWidth.value / image.width, canvasHeight.value / image.height)

      const scaledWidth = image.width * scale
      const scaledHeight = image.height * scale
      const x = (canvasWidth.value - scaledWidth) / 2
      const y = (canvasHeight.value - scaledHeight) / 2

      ctx.drawImage(image, x, y, scaledWidth, scaledHeight)
    }

    // Render individual element
    const renderElement = (element) => {
      ctx.save()

      // Apply transformations
      const centerX = element.x + element.width / 2
      const centerY = element.y + element.height / 2

      ctx.translate(centerX, centerY)
      ctx.rotate(((element.rotation || 0) * Math.PI) / 180)
      ctx.scale(element.scaleX || 1, element.scaleY || 1)
      ctx.translate(-element.width / 2, -element.height / 2)

      if (element.type === 'text') {
        renderTextElement(element)
      } else if (element.type === 'sticker') {
        renderStickerElement(element)
      } else if (element.type === 'shape') {
        renderShapeElement(element)
      } else if (element.type === 'image') {
        renderImageElement(element)
      }

      // Render selection outline
      if (element.id === canvasStore.selectedElementId) {
        renderSelectionOutline(element)
      }

      ctx.restore()
    }

    // Render text element
    const renderTextElement = (element) => {
      ctx.font = `${element.fontWeight || 'normal'} ${element.fontStyle || 'normal'} ${element.fontSize}px ${element.fontFamily}`
      ctx.textBaseline = 'top'

      // Calculate text position based on alignment
      let textX = 0
      const textAlign = element.textAlign || 'left'

      if (textAlign === 'center') {
        textX = element.width / 2
        ctx.textAlign = 'center'
      } else if (textAlign === 'right') {
        textX = element.width
        ctx.textAlign = 'right'
      } else {
        textX = 0
        ctx.textAlign = 'left'
      }

      // Render text shadow
      if (element.shadowColor && element.shadowBlur > 0) {
        ctx.shadowColor = element.shadowColor
        ctx.shadowBlur = element.shadowBlur
        ctx.shadowOffsetX = element.shadowOffsetX || 0
        ctx.shadowOffsetY = element.shadowOffsetY || 0
      }

      // Render text stroke
      if (element.strokeColor && element.strokeWidth > 0) {
        ctx.strokeStyle = element.strokeColor
        ctx.lineWidth = element.strokeWidth
        ctx.strokeText(element.text, textX, 0)
      }

      // Render text fill
      ctx.fillStyle = element.color
      ctx.fillText(element.text, textX, 0)

      // Reset shadow
      ctx.shadowColor = 'transparent'
      ctx.shadowBlur = 0
    }

    // Render sticker element
    const renderStickerElement = (element) => {
      if (element.stickerType === 'emoji' && element.content) {
        // Render emoji as text
        ctx.font = `${element.height * 0.8}px Arial`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(element.content, element.width / 2, element.height / 2)
      } else if (element.image) {
        // Render image sticker
        ctx.drawImage(element.image, 0, 0, element.width, element.height)
      }
    }

    // Render shape element
    const renderShapeElement = (element) => {
      const { width, height, fill, stroke, strokeWidth, shapeType } = element

      // Set fill style
      if (fill) {
        ctx.fillStyle = fill
      }

      // Set stroke style
      if (stroke && strokeWidth > 0) {
        ctx.strokeStyle = stroke
        ctx.lineWidth = strokeWidth
      }

      // Draw shape based on type
      switch (shapeType) {
        case 'rectangle':
          if (fill) ctx.fillRect(0, 0, width, height)
          if (stroke && strokeWidth > 0) ctx.strokeRect(0, 0, width, height)
          break

        case 'circle':
          ctx.beginPath()
          ctx.arc(width / 2, height / 2, Math.min(width, height) / 2, 0, 2 * Math.PI)
          if (fill) ctx.fill()
          if (stroke && strokeWidth > 0) ctx.stroke()
          break

        case 'triangle':
          ctx.beginPath()
          ctx.moveTo(width / 2, 0)
          ctx.lineTo(width, height)
          ctx.lineTo(0, height)
          ctx.closePath()
          if (fill) ctx.fill()
          if (stroke && strokeWidth > 0) ctx.stroke()
          break

        case 'star':
          drawStar(
            ctx,
            width / 2,
            height / 2,
            5,
            Math.min(width, height) / 2,
            Math.min(width, height) / 4,
          )
          if (fill) ctx.fill()
          if (stroke && strokeWidth > 0) ctx.stroke()
          break

        case 'heart':
          drawHeart(ctx, width / 2, height / 2, Math.min(width, height) / 2)
          if (fill) ctx.fill()
          if (stroke && strokeWidth > 0) ctx.stroke()
          break

        case 'hexagon':
          drawHexagon(ctx, width / 2, height / 2, Math.min(width, height) / 2)
          if (fill) ctx.fill()
          if (stroke && strokeWidth > 0) ctx.stroke()
          break
      }
    }

    // Helper function to draw star
    const drawStar = (ctx, cx, cy, spikes, outerRadius, innerRadius) => {
      let rot = (Math.PI / 2) * 3
      let x = cx
      let y = cy
      const step = Math.PI / spikes

      ctx.beginPath()
      ctx.moveTo(cx, cy - outerRadius)

      for (let i = 0; i < spikes; i++) {
        x = cx + Math.cos(rot) * outerRadius
        y = cy + Math.sin(rot) * outerRadius
        ctx.lineTo(x, y)
        rot += step

        x = cx + Math.cos(rot) * innerRadius
        y = cy + Math.sin(rot) * innerRadius
        ctx.lineTo(x, y)
        rot += step
      }

      ctx.lineTo(cx, cy - outerRadius)
      ctx.closePath()
    }

    // Helper function to draw heart
    const drawHeart = (ctx, cx, cy, size) => {
      ctx.beginPath()
      ctx.moveTo(cx, cy + size / 4)
      ctx.bezierCurveTo(
        cx,
        cy - size / 8,
        cx - size / 2,
        cy - size / 8,
        cx - size / 2,
        cy + size / 8,
      )
      ctx.bezierCurveTo(cx - size / 2, cy + size / 2, cx, cy + size / 2, cx, cy + size)
      ctx.bezierCurveTo(
        cx,
        cy + size / 2,
        cx + size / 2,
        cy + size / 2,
        cx + size / 2,
        cy + size / 8,
      )
      ctx.bezierCurveTo(cx + size / 2, cy - size / 8, cx, cy - size / 8, cx, cy + size / 4)
      ctx.closePath()
    }

    // Helper function to draw hexagon
    const drawHexagon = (ctx, cx, cy, size) => {
      ctx.beginPath()
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI) / 3
        const x = cx + size * Math.cos(angle)
        const y = cy + size * Math.sin(angle)
        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }
      ctx.closePath()
    }

    // Render image element
    const renderImageElement = (element) => {
      if (!element.src) return

      // Create image if not cached
      if (!element._imageCache) {
        element._imageCache = new Image()
        element._imageCache.crossOrigin = 'anonymous'
        element._imageCache.onload = () => {
          render() // Re-render when image loads
        }
        element._imageCache.src = element.src
      }

      const img = element._imageCache
      if (!img.complete) return // Image not loaded yet

      // Apply filters if any
      if (element.filters) {
        const { brightness, contrast, saturation, blur } = element.filters
        if (brightness !== 100 || contrast !== 100 || saturation !== 100 || blur > 0) {
          ctx.filter = `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%) blur(${blur}px)`
        }
      }

      // Draw image
      ctx.drawImage(img, 0, 0, element.width, element.height)

      // Reset filter
      ctx.filter = 'none'
    }

    // Export methods
    const exportAsDataURL = (format = 'png', quality = 0.9) => {
      if (!canvasRef.value) return null

      const mimeType = format === 'jpg' || format === 'jpeg' ? 'image/jpeg' : `image/${format}`
      return canvasRef.value.toDataURL(mimeType, quality)
    }

    const exportAsBlob = (format = 'png', quality = 0.9) => {
      return new Promise((resolve) => {
        if (!canvasRef.value) {
          resolve(null)
          return
        }

        const mimeType = format === 'jpg' || format === 'jpeg' ? 'image/jpeg' : `image/${format}`
        canvasRef.value.toBlob(resolve, mimeType, quality)
      })
    }

    // Render selection outline
    const renderSelectionOutline = (element) => {
      ctx.strokeStyle = '#007aff'
      ctx.lineWidth = 2
      ctx.setLineDash([5, 5])
      ctx.strokeRect(-2, -2, element.width + 4, element.height + 4)
      ctx.setLineDash([])
    }

    // Get mouse/touch position relative to canvas
    const getEventPosition = (event) => {
      const rect = canvasRef.value.getBoundingClientRect()
      const scaleX = canvasWidth.value / rect.width
      const scaleY = canvasHeight.value / rect.height

      let clientX, clientY

      if (event.touches) {
        clientX = event.touches[0].clientX
        clientY = event.touches[0].clientY
      } else {
        clientX = event.clientX
        clientY = event.clientY
      }

      return {
        x: (clientX - rect.left) * scaleX,
        y: (clientY - rect.top) * scaleY,
      }
    }

    // Find element at position
    const getElementAtPosition = (x, y) => {
      // Check elements in reverse order (top to bottom)
      for (let i = canvasStore.elements.length - 1; i >= 0; i--) {
        const element = canvasStore.elements[i]
        if (
          x >= element.x &&
          x <= element.x + element.width &&
          y >= element.y &&
          y <= element.y + element.height
        ) {
          return element
        }
      }
      return null
    }

    // Mouse event handlers
    const handleMouseDown = (event) => {
      const pos = getEventPosition(event)
      const element = getElementAtPosition(pos.x, pos.y)

      if (element) {
        selectedElement.value = element
        canvasStore.selectElement(element.id)
        isDragging.value = true
        dragStartPos.value = { x: pos.x - element.x, y: pos.y - element.y }
        hapticFeedback.selectionChanged()
        emit('element-selected', element)
      } else {
        canvasStore.selectElement(null)
        selectedElement.value = null
      }
    }

    const handleMouseMove = (event) => {
      if (!isDragging.value || !selectedElement.value) return

      const pos = getEventPosition(event)
      const newX = pos.x - dragStartPos.value.x
      const newY = pos.y - dragStartPos.value.y

      canvasStore.updateElement(selectedElement.value.id, { x: newX, y: newY })
      emit('element-moved', selectedElement.value)
    }

    const handleMouseUp = () => {
      isDragging.value = false
      selectedElement.value = null
    }

    // Touch event handlers
    const handleTouchStart = (event) => {
      event.preventDefault()

      const now = Date.now()
      const timeDiff = now - lastTouchTime.value
      lastTouchTime.value = now

      // Handle double tap
      if (timeDiff < 300) {
        const pos = getEventPosition(event)
        const element = getElementAtPosition(pos.x, pos.y)
        if (element) {
          hapticFeedback.impactOccurred('medium')
          // Could trigger edit mode here
        }
        return
      }

      handleMouseDown(event)
    }

    const handleTouchMove = (event) => {
      event.preventDefault()
      handleMouseMove(event)
    }

    const handleTouchEnd = (event) => {
      event.preventDefault()
      handleMouseUp()
    }

    // Export canvas as image
    const exportAsImage = (format = 'png', quality = 1.0) => {
      if (!canvasRef.value) return null

      return new Promise((resolve) => {
        canvasRef.value.toBlob(
          (blob) => {
            resolve(blob)
          },
          `image/${format}`,
          quality,
        )
      })
    }

    return {
      canvasRef,
      containerRef,
      canvasWidth,
      canvasHeight,
      isLoading,
      loadingText,
      isDragging,
      snapThreshold,
      handleMouseDown,
      handleMouseMove,
      handleMouseUp,
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      exportAsImage,
      exportAsDataURL,
      exportAsBlob,
      render,
    }
  },
}
</script>

<style lang="scss" scoped>
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.8) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.6) 0%, transparent 50%),
    var(--tg-theme-secondary-bg-color, #f8f9fa);
  border-radius: 12px;
  overflow: hidden;
  padding: 20px;
}

canvas {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
  background: white;
  cursor: crosshair;
  transition: all 0.3s ease;

  &:hover {
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.1);
  }

  &.dragging {
    cursor: grabbing;
    box-shadow:
      0 16px 64px rgba(0, 0, 0, 0.2),
      0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.canvas-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 10;

  p {
    margin-top: 16px;
    color: var(--tg-theme-hint-color, #8e8e93);
    font-size: 14px;
  }
}
</style>

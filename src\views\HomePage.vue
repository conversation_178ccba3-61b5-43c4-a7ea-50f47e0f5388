<template>
  <div class="home-page">
    <OnboardingSlides :is-visible="true" @project-created="handleProjectCreated" />
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { useCanvasStore } from '@/stores/canvas'
import OnboardingSlides from '@/components/OnboardingSlides.vue'

export default {
  name: 'HomePage',
  components: {
    OnboardingSlides,
  },
  setup() {
    const router = useRouter()
    const canvasStore = useCanvasStore()

    const handleProjectCreated = (projectData) => {
      // Initialize canvas with project configuration
      canvasStore.initializeCanvas({
        width: projectData.width,
        height: projectData.height,
        background: projectData.background,
      })

      // Navigate to editor
      router.push('/editor')
    }

    return {
      handleProjectCreated,
    }
  },
}
</script>

<style lang="scss" scoped>
.home-page {
  width: 100%;
  height: 100vh;
}
</style>
